﻿using Tec.Ecademy.Academics.Infra.Data;
using Tec.Rad.Cqs;
using Tec.Rad.Extensions.Core;
using Tec.Rad.Extensions.Data.Marten;
using Tec.Rad.Modular;
using Volo.Abp.Modularity;

namespace Tec.Ecademy.Academics.Infra;

[DependsOn(
    typeof(RadMartenModule),
    typeof(RadExtensionsModule),
    typeof(AcademicsModule)
)]
public class AcademicsInfraModule: RadModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {

        this.RegisterForCqs<AcademicsInfraModule>();
        this.RegisterDocDb<AcademicsDb>();
    }

        
}