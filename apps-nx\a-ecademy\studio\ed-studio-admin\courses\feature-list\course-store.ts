import { computed, inject, Injectable, resource } from "@angular/core";
import { CoursesApiProxy, CreateCourseCommand } from "@ed/share/data-content"
import { apiResultFrom, executeApi, executeAsync } from "@tec/rad-core/utils";
import { RadSignalStore } from "@tec/rad-xui/services";


@Injectable()
export class CourseStore extends RadSignalStore {

    courseData = inject(CoursesApiProxy);

    constructor() {
        super();
    }


    private _courses = resource({

        loader: () => executeApi(this.courseData.getCourses({}))

    })

    courses = computed(() => this._courses.value()?.items);
    loading = computed(() => this._courses.isLoading());


    async createCourse(command: CreateCourseCommand){

        const result = await apiResultFrom(this.courseData.createCourse(command));
        if(result.isSuccess){
            this._courses.reload();
        }

    }

}