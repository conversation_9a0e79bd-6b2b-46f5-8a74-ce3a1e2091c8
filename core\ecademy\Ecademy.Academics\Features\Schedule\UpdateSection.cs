﻿using Tec.Rad.Apis;
using Tec.Rad.Features.Forms;

namespace Tec.Ecademy.Academics.Features.Schedule;

internal class UpdateSection
{
    public class Command : CommandRequest<Result>, IFormCommand
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public DateOnly StartDate { get; set; }
        public DateOnly EndDate { get; set; }
        public ReleaseStatus ReleaseStatus { get; set; }
    }

    public class Result : ResultObject
    {
        public Segment Segment { get; set; }
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            var section = await DocDb.GetAsync<Segment>(Input.Id);
            if (section == null)
            {
                throw new BusinessException("Subsection not found");
            }

            section.Name = Input.Name;
            section.StartDate = Input.StartDate;
            section.EndDate = Input.EndDate;
            section.ReleaseStatus = Input.ReleaseStatus;
            DocDb.Update(section);
            Output.Segment = section;
        }
    }

    internal class Endpoint : CommandEndpoint<Command, Result>
    {
    }
}