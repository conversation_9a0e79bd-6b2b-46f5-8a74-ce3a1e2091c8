import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { INavItem, injectTabReference } from '@tec/rad-core/abstractions';
import { injectRouteParam } from '@tec/rad-core/utils';
import { Rad<PERSON>ontentLayout, RadHeader, RadRouterOutlet, RadSideMaster, RadSideNav } from '@tec/rad-ui/layout';
import { RadSidebar } from '@tec/rad-ui/navbar';



@Component({
  selector: 'app-course',
  template: `
  <rad-side-nav [divider]="true" drawerWidth="180" >
    <rad-content-layout radSideNavDrawer class="w-full bg-slate-50">
      
      <rad-header radContentHeader title="Grade 5 Pep" [divider]="true">
  
      </rad-header>
  
        <rad-sidebar side class="h-full overflow-clip"
          [appearance]="'default'"
          [navigation]="nav"
          [inner]="true"
          [mode]="'side'"
          name="course-sidebar-navigation"
          [opened]="true"
        ></rad-sidebar>
  
    </rad-content-layout>
  
    <rad-router-outlet></rad-router-outlet>
    <!-- <div class="flex flex-col flex-auto animation-2">
      <router-outlet *ngIf="true"></router-outlet>
    </div>
     -->
  
  
  </rad-side-nav>
  `,
 
  imports: [
    CommonModule,
    RadSideNav,
    RadSidebar,
    RouterModule,
    RadHeader,
    RadContentLayout,
    //Add required imports here
    RadRouterOutlet
  ]
})
export class CoursePage {


  #id = injectRouteParam('courseId');
  #tabRef = injectTabReference();

  protected nav: INavItem[] = [
    {
      title: 'Details',
      icon: 'ri-information-line',
      link: 'view/CourseDetail',
    },
    {
      title: 'Runs',
      icon: 'ri-information-line',
      link: 'view/CourseRuns',
    },
    {
      type: 'divider',
    },
    {
      type: 'group',
      title: 'Subjects',
      children: [
        {
          title: 'Ability',
          iconType: 'dot',
          icon: 'primary',
        },
        {
          title: 'Language Arts',
          iconType: 'dot',
          icon: 'success',
        },
        {
          title: 'Mathematics',
          iconType: 'dot',
          icon: 'primary',
        }
      ]
    }






  ];




}