import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input, Injector } from '@angular/core';
import { ActionItem } from '@tec/rad-ui/common';
import { RadPage } from '@tec/rad-ui/layout';
import { CourseStore } from './course-store';
import { RadEjGrid } from '@tec/rad-xui/ej-grid';
import { AppDialogService, AppNavigationService } from '@tec/rad-core/abstractions';
import { CreateCourseView } from './create-course-view';
import { RadGridColumn } from '@tec/rad-xui/common';
import { CourseDto } from '@ed/share/data-content';

        
@Component({
    selector: 'ed-course-list',
    template: `
    <rad-page title='Courses' layout="card" [actions]="actions" [loading]="store.loading()">
        <rad-ej-grid [dataSource]="store.courses()" [columns]="columns" />
    </rad-page>
    `,
    styles: [],
    imports:[
        CommonModule,
        RadPage,
        RadEjGrid

    ],
    providers:[CourseStore]
    
})
export class CourseList {
        

    protected store = inject(CourseStore);
    #dialog = inject(AppDialogService);
    #nav = inject(AppNavigationService);
    #injector = inject(Injector);

    actions: ActionItem[] = [
        { label: 'New', icon: 'add', display:'primary', action: this.createCourse.bind(this)  },
       
    ];

    columns: RadGridColumn[] = [
        { field: 'name', label: 'Name', type: 'text', action: (item)=> this.viewCourse(item) },
        { field: 'description', label: 'Description' },
        { field: 'status', label: 'Status' },
    ];
        

    private async createCourse() {
        await this.#dialog.show(CreateCourseView, undefined, {injector: this.#injector});
    }
    
    private viewCourse(course: CourseDto) {
        this.#nav.goTo('Course', course.id);
    }
        
}