﻿using Tec.Rad.Data;

namespace Tec.Ecademy.Academics.Features.Schedule.Shared;

public class RunSegmentNode: ISqlView
{
    public Guid Id { get; set; }

    
    public Guid CycleId { get; set; }

    public string NodeType { get; set; }

    public string Name { get; set; }

    public Guid? ParentId { get; set; }

    public ReleaseStatus ReleaseStatus { get; set; }

    public DateOnly? StartDate { get; set; }

    public DateOnly? EndDate { get; set; }

    public int Position { get; set; }

}