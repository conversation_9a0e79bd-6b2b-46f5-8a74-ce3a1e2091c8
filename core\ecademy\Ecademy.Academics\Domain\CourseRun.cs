﻿namespace Tec.Ecademy.Academics.Domain;

public class CourseRun: DocEntity
{
    
    public CourseRun(Guid id) : base(id)
    {
    }
    
    public string Name { get; set; }
    public string Description { get; set; }
    public Guid CourseId { get; set; }
    public CourseRunStatus Status { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public int MaxEnrollments { get; set; }
    public int CurrentEnrollments { get; set; }
    public bool AllowLateEnrollment { get; set; }
    public DateTimeOffset? EnrollmentDeadline { get; set; }
    public string? InstructorNotes { get; set; }
    public Guid? YearId { get; set; }
}