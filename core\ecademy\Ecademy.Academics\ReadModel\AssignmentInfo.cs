﻿using Tec.Rad.Data;
using Volo.Abp.Application.Dtos;

namespace Tec.Ecademy.Academics.ReadModel;


public class AssignmentInfo : EntityDto<Guid>, ISqlView
{
    public string AssignmentNumber { get; set; }
    
    public Guid CycleId { get; init; }

    public Guid ActivityId { get; set; }

    public string ActivityType { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }
    
    public Guid CourseSubjectId { get; set; }

    public string Subject { get; set; }

    public ReleaseStatus ReleaseStatus { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public DateTime? DueDate { get; set; }

    public Guid AssignmentTypeId { get; set; }

    public string AssignmentType { get; set; }
    
    public Guid? SectionId { get; set; }

    public string Section { get; set; }

    public Guid? SubsectionId { get; set; }

    public string Subsection { get; set; }


    public int Position { get; set; }

   

}