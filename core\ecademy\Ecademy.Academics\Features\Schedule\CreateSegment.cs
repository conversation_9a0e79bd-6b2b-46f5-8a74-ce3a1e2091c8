﻿using Tec.Ecademy.Academics.Features.Schedule.Shared;
using Tec.Rad.Apis;
using Tec.Rad.Features.Forms;
using Tec.Rad.SqlKata;
using Tec.Rad.Utils;

namespace Tec.Ecademy.Academics.Features.Schedule;

internal class CreateSegment
{
    public class Command : CommandRequest<Result>, IFormCommand
    {
        public Guid CourseRunId { get; set; }
        public string Name { get; set; }
        public DateOnly StartDate { get; set; }
        public DateOnly EndDate { get; set; }
    }

    public class Result : ResultObject
    {
        public Segment Segment { get; set; }
        public RunSegmentNode Node { get; set; }
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            var section = new Segment(GuidGenerator.New());
            section.CourseRunId = Input.CourseRunId;
            section.Name = Input.Name;
            section.StartDate = Input.StartDate;
            section.EndDate = Input.EndDate;
            section.ReleaseStatus = ReleaseStatus.Draft;
            await DocDb.InsertAsync(section);
            var node = await SqlDb.ExpressionQuery<RunSegmentNode>().FirstOrDefaultAsync(n => n.Id, section.Id);
            Output.Segment = section;
            Output.Node = node;
        }
    }

    internal class Endpoint : CommandEndpoint<Command, Result>
    {
    }
}