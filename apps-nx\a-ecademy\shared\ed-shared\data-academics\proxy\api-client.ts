//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

export const ACADEMICS_API_URL = new InjectionToken<string>('ACADEMICS_API_URL');

export interface ICourseRunsApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    createCourseRun(body?: ICreateCourseRunCommand | undefined): Observable<CreateCourseRunResult>;
    /**
     * @param courseId (optional) 
     * @return OK
     */
    getCourseRuns(courseId?: string | undefined): Observable<GetCourseRunsResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    updateCourseRun(body?: IUpdateCourseRunCommand | undefined): Observable<UpdateCourseRunResult>;
}

@Injectable({
    providedIn: 'root'
})
export class CourseRunsApiProxy implements ICourseRunsApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(ACADEMICS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createCourseRun(body?: ICreateCourseRunCommand | undefined): Observable<CreateCourseRunResult> {
        let url_ = this.baseUrl + "/api/academics/course-runs/create-course-run";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateCourseRun(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateCourseRun(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CreateCourseRunResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CreateCourseRunResult>;
        }));
    }

    protected processCreateCourseRun(response: HttpResponseBase): Observable<CreateCourseRunResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CreateCourseRunResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param courseId (optional) 
     * @return OK
     */
    getCourseRuns(courseId?: string | undefined): Observable<GetCourseRunsResult> {
        let url_ = this.baseUrl + "/api/academics/course-runs/get-course-runs?";
        if (courseId === null)
            throw new Error("The parameter 'courseId' cannot be null.");
        else if (courseId !== undefined)
            url_ += "CourseId=" + encodeURIComponent("" + courseId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCourseRuns(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCourseRuns(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCourseRunsResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCourseRunsResult>;
        }));
    }

    protected processGetCourseRuns(response: HttpResponseBase): Observable<GetCourseRunsResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCourseRunsResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updateCourseRun(body?: IUpdateCourseRunCommand | undefined): Observable<UpdateCourseRunResult> {
        let url_ = this.baseUrl + "/api/academics/course-runs/update-course-run";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateCourseRun(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateCourseRun(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<UpdateCourseRunResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<UpdateCourseRunResult>;
        }));
    }

    protected processUpdateCourseRun(response: HttpResponseBase): Observable<UpdateCourseRunResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = UpdateCourseRunResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface ICourseworksApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    createCoursework(body?: ICreateCourseworkCommand | undefined): Observable<CreateCourseworkResult>;
    /**
     * @param subsectionId (optional) 
     * @return OK
     */
    getCoursework(subsectionId?: string | undefined): Observable<GetCourseworkResult>;
    /**
     * @param assignmentId (optional) 
     * @return OK
     */
    getCourseworkDetail(assignmentId?: string | undefined): Observable<GetCourseworkDetailResult>;
    /**
     * @param id (optional) 
     * @param cycleId (optional) 
     * @param subjectId (optional) 
     * @param unitId (optional) 
     * @param sectionId (optional) 
     * @param releaseStatus (optional) 
     * @param searchText (optional) 
     * @return OK
     */
    getCourseworkList(id?: string | undefined, cycleId?: string | undefined, subjectId?: string | undefined, unitId?: string | undefined, sectionId?: string | undefined, releaseStatus?: ReleaseStatus | undefined, searchText?: string | undefined): Observable<GetCourseworkListResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    moveCoursework(body?: IMoveCourseworkCommand | undefined): Observable<MoveCourseworkResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    removeCoursework(body?: IRemoveCourseworkCommand | undefined): Observable<ResultObject>;
    /**
     * @param body (optional) 
     * @return OK
     */
    updateCoursework(body?: IUpdateCourseworkCommand | undefined): Observable<UpdateCourseworkResult>;
}

@Injectable({
    providedIn: 'root'
})
export class CourseworksApiProxy implements ICourseworksApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(ACADEMICS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createCoursework(body?: ICreateCourseworkCommand | undefined): Observable<CreateCourseworkResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/create-coursework";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateCoursework(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateCoursework(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CreateCourseworkResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CreateCourseworkResult>;
        }));
    }

    protected processCreateCoursework(response: HttpResponseBase): Observable<CreateCourseworkResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CreateCourseworkResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param subsectionId (optional) 
     * @return OK
     */
    getCoursework(subsectionId?: string | undefined): Observable<GetCourseworkResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/get-coursework?";
        if (subsectionId === null)
            throw new Error("The parameter 'subsectionId' cannot be null.");
        else if (subsectionId !== undefined)
            url_ += "SubsectionId=" + encodeURIComponent("" + subsectionId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCoursework(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCoursework(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCourseworkResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCourseworkResult>;
        }));
    }

    protected processGetCoursework(response: HttpResponseBase): Observable<GetCourseworkResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCourseworkResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param assignmentId (optional) 
     * @return OK
     */
    getCourseworkDetail(assignmentId?: string | undefined): Observable<GetCourseworkDetailResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/get-coursework-detail?";
        if (assignmentId === null)
            throw new Error("The parameter 'assignmentId' cannot be null.");
        else if (assignmentId !== undefined)
            url_ += "AssignmentId=" + encodeURIComponent("" + assignmentId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCourseworkDetail(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCourseworkDetail(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCourseworkDetailResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCourseworkDetailResult>;
        }));
    }

    protected processGetCourseworkDetail(response: HttpResponseBase): Observable<GetCourseworkDetailResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCourseworkDetailResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param id (optional) 
     * @param cycleId (optional) 
     * @param subjectId (optional) 
     * @param unitId (optional) 
     * @param sectionId (optional) 
     * @param releaseStatus (optional) 
     * @param searchText (optional) 
     * @return OK
     */
    getCourseworkList(id?: string | undefined, cycleId?: string | undefined, subjectId?: string | undefined, unitId?: string | undefined, sectionId?: string | undefined, releaseStatus?: ReleaseStatus | undefined, searchText?: string | undefined): Observable<GetCourseworkListResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/get-coursework-list?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "Id=" + encodeURIComponent("" + id) + "&";
        if (cycleId === null)
            throw new Error("The parameter 'cycleId' cannot be null.");
        else if (cycleId !== undefined)
            url_ += "CycleId=" + encodeURIComponent("" + cycleId) + "&";
        if (subjectId === null)
            throw new Error("The parameter 'subjectId' cannot be null.");
        else if (subjectId !== undefined)
            url_ += "SubjectId=" + encodeURIComponent("" + subjectId) + "&";
        if (unitId === null)
            throw new Error("The parameter 'unitId' cannot be null.");
        else if (unitId !== undefined)
            url_ += "UnitId=" + encodeURIComponent("" + unitId) + "&";
        if (sectionId === null)
            throw new Error("The parameter 'sectionId' cannot be null.");
        else if (sectionId !== undefined)
            url_ += "SectionId=" + encodeURIComponent("" + sectionId) + "&";
        if (releaseStatus === null)
            throw new Error("The parameter 'releaseStatus' cannot be null.");
        else if (releaseStatus !== undefined)
            url_ += "ReleaseStatus=" + encodeURIComponent("" + releaseStatus) + "&";
        if (searchText === null)
            throw new Error("The parameter 'searchText' cannot be null.");
        else if (searchText !== undefined)
            url_ += "SearchText=" + encodeURIComponent("" + searchText) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCourseworkList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCourseworkList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCourseworkListResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCourseworkListResult>;
        }));
    }

    protected processGetCourseworkList(response: HttpResponseBase): Observable<GetCourseworkListResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCourseworkListResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    moveCoursework(body?: IMoveCourseworkCommand | undefined): Observable<MoveCourseworkResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/move-coursework";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processMoveCoursework(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processMoveCoursework(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<MoveCourseworkResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<MoveCourseworkResult>;
        }));
    }

    protected processMoveCoursework(response: HttpResponseBase): Observable<MoveCourseworkResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = MoveCourseworkResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    removeCoursework(body?: IRemoveCourseworkCommand | undefined): Observable<ResultObject> {
        let url_ = this.baseUrl + "/api/academics/courseworks/remove-coursework";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRemoveCoursework(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRemoveCoursework(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResultObject>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResultObject>;
        }));
    }

    protected processRemoveCoursework(response: HttpResponseBase): Observable<ResultObject> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResultObject.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updateCoursework(body?: IUpdateCourseworkCommand | undefined): Observable<UpdateCourseworkResult> {
        let url_ = this.baseUrl + "/api/academics/courseworks/update-coursework";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateCoursework(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateCoursework(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<UpdateCourseworkResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<UpdateCourseworkResult>;
        }));
    }

    protected processUpdateCoursework(response: HttpResponseBase): Observable<UpdateCourseworkResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = UpdateCourseworkResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface ICourseworkTypeApiProxy {
    /**
     * @param id (optional) 
     * @return OK
     */
    get(id?: string | undefined): Observable<CourseworkType>;
    /**
     * @param body (optional) 
     * @return OK
     */
    create(body?: ICourseworkType | undefined): Observable<CourseworkType>;
    /**
     * @param id (optional) 
     * @param body (optional) 
     * @return OK
     */
    update(id?: string | undefined, body?: CourseworkType | undefined): Observable<CourseworkType>;
    /**
     * @param id (optional) 
     * @return OK
     */
    delete(id?: string | undefined): Observable<void>;
    /**
     * @param skip (optional) 
     * @param take (optional) 
     * @param sortBy (optional) 
     * @param sortDescending (optional) 
     * @param sortDirection (optional) 
     * @return OK
     */
    getList(skip?: number | undefined, take?: number | undefined, sortBy?: string | undefined, sortDescending?: boolean | undefined, sortDirection?: string | undefined): Observable<PagedResultDtoOfCourseworkType>;
}

@Injectable({
    providedIn: 'root'
})
export class CourseworkTypeApiProxy implements ICourseworkTypeApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(ACADEMICS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param id (optional) 
     * @return OK
     */
    get(id?: string | undefined): Observable<CourseworkType> {
        let url_ = this.baseUrl + "/api/academics/coursework-type/get?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGet(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGet(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CourseworkType>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CourseworkType>;
        }));
    }

    protected processGet(response: HttpResponseBase): Observable<CourseworkType> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CourseworkType.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    create(body?: ICourseworkType | undefined): Observable<CourseworkType> {
        let url_ = this.baseUrl + "/api/academics/coursework-type/create";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CourseworkType>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CourseworkType>;
        }));
    }

    protected processCreate(response: HttpResponseBase): Observable<CourseworkType> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CourseworkType.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param id (optional) 
     * @param body (optional) 
     * @return OK
     */
    update(id?: string | undefined, body?: CourseworkType | undefined): Observable<CourseworkType> {
        let url_ = this.baseUrl + "/api/academics/coursework-type/update?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CourseworkType>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CourseworkType>;
        }));
    }

    protected processUpdate(response: HttpResponseBase): Observable<CourseworkType> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CourseworkType.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param id (optional) 
     * @return OK
     */
    delete(id?: string | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/academics/coursework-type/delete?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDelete(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDelete(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processDelete(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param skip (optional) 
     * @param take (optional) 
     * @param sortBy (optional) 
     * @param sortDescending (optional) 
     * @param sortDirection (optional) 
     * @return OK
     */
    getList(skip?: number | undefined, take?: number | undefined, sortBy?: string | undefined, sortDescending?: boolean | undefined, sortDirection?: string | undefined): Observable<PagedResultDtoOfCourseworkType> {
        let url_ = this.baseUrl + "/api/academics/coursework-type/get-list?";
        if (skip === null)
            throw new Error("The parameter 'skip' cannot be null.");
        else if (skip !== undefined)
            url_ += "Skip=" + encodeURIComponent("" + skip) + "&";
        if (take === null)
            throw new Error("The parameter 'take' cannot be null.");
        else if (take !== undefined)
            url_ += "Take=" + encodeURIComponent("" + take) + "&";
        if (sortBy === null)
            throw new Error("The parameter 'sortBy' cannot be null.");
        else if (sortBy !== undefined)
            url_ += "SortBy=" + encodeURIComponent("" + sortBy) + "&";
        if (sortDescending === null)
            throw new Error("The parameter 'sortDescending' cannot be null.");
        else if (sortDescending !== undefined)
            url_ += "SortDescending=" + encodeURIComponent("" + sortDescending) + "&";
        if (sortDirection === null)
            throw new Error("The parameter 'sortDirection' cannot be null.");
        else if (sortDirection !== undefined)
            url_ += "SortDirection=" + encodeURIComponent("" + sortDirection) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<PagedResultDtoOfCourseworkType>;
                }
            } else
                return _observableThrow(response_) as any as Observable<PagedResultDtoOfCourseworkType>;
        }));
    }

    protected processGetList(response: HttpResponseBase): Observable<PagedResultDtoOfCourseworkType> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = PagedResultDtoOfCourseworkType.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IScheduleApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    createSegment(body?: ICreateSegmentCommand | undefined): Observable<CreateSegmentResult>;
    /**
     * @param cycleId (optional) 
     * @return OK
     */
    getRunSegments(cycleId?: string | undefined): Observable<GetRunSegmentsResult>;
    /**
     * @param subsectionId (optional) 
     * @return OK
     */
    getSegmentDetail(subsectionId?: string | undefined): Observable<SubsectionDetailOutput>;
    /**
     * @param body (optional) 
     * @return OK
     */
    moveSegment(body?: IMoveSegmentInput | undefined): Observable<MoveSegmentResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    removeSegment(body?: IRemoveSegmentCommand | undefined): Observable<ResultObject>;
    /**
     * @param body (optional) 
     * @return OK
     */
    updateSection(body?: IUpdateSectionCommand | undefined): Observable<UpdateSectionResult>;
}

@Injectable({
    providedIn: 'root'
})
export class ScheduleApiProxy implements IScheduleApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(ACADEMICS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createSegment(body?: ICreateSegmentCommand | undefined): Observable<CreateSegmentResult> {
        let url_ = this.baseUrl + "/api/academics/schedule/create-segment";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateSegment(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateSegment(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CreateSegmentResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CreateSegmentResult>;
        }));
    }

    protected processCreateSegment(response: HttpResponseBase): Observable<CreateSegmentResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CreateSegmentResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param cycleId (optional) 
     * @return OK
     */
    getRunSegments(cycleId?: string | undefined): Observable<GetRunSegmentsResult> {
        let url_ = this.baseUrl + "/api/academics/schedule/get-run-segments?";
        if (cycleId === null)
            throw new Error("The parameter 'cycleId' cannot be null.");
        else if (cycleId !== undefined)
            url_ += "CycleId=" + encodeURIComponent("" + cycleId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetRunSegments(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetRunSegments(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetRunSegmentsResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetRunSegmentsResult>;
        }));
    }

    protected processGetRunSegments(response: HttpResponseBase): Observable<GetRunSegmentsResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetRunSegmentsResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param subsectionId (optional) 
     * @return OK
     */
    getSegmentDetail(subsectionId?: string | undefined): Observable<SubsectionDetailOutput> {
        let url_ = this.baseUrl + "/api/academics/schedule/get-segment-detail?";
        if (subsectionId === null)
            throw new Error("The parameter 'subsectionId' cannot be null.");
        else if (subsectionId !== undefined)
            url_ += "SubsectionId=" + encodeURIComponent("" + subsectionId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetSegmentDetail(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetSegmentDetail(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<SubsectionDetailOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<SubsectionDetailOutput>;
        }));
    }

    protected processGetSegmentDetail(response: HttpResponseBase): Observable<SubsectionDetailOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = SubsectionDetailOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    moveSegment(body?: IMoveSegmentInput | undefined): Observable<MoveSegmentResult> {
        let url_ = this.baseUrl + "/api/academics/schedule/move-segment";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processMoveSegment(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processMoveSegment(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<MoveSegmentResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<MoveSegmentResult>;
        }));
    }

    protected processMoveSegment(response: HttpResponseBase): Observable<MoveSegmentResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = MoveSegmentResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    removeSegment(body?: IRemoveSegmentCommand | undefined): Observable<ResultObject> {
        let url_ = this.baseUrl + "/api/academics/schedule/remove-segment";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRemoveSegment(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRemoveSegment(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResultObject>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResultObject>;
        }));
    }

    protected processRemoveSegment(response: HttpResponseBase): Observable<ResultObject> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResultObject.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updateSection(body?: IUpdateSectionCommand | undefined): Observable<UpdateSectionResult> {
        let url_ = this.baseUrl + "/api/academics/schedule/update-section";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateSection(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateSection(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<UpdateSectionResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<UpdateSectionResult>;
        }));
    }

    protected processUpdateSection(response: HttpResponseBase): Observable<UpdateSectionResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = UpdateSectionResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class AssignmentInfo implements IAssignmentInfo {
    id: string;
    assignmentNumber: string | undefined;
    cycleId: string;
    activityId: string;
    activityType: string | undefined;
    name: string | undefined;
    description: string | undefined;
    courseSubjectId: string;
    subject: string | undefined;
    releaseStatus: ReleaseStatus;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    assignmentTypeId: string;
    assignmentType: string | undefined;
    sectionId: string | undefined;
    section: string | undefined;
    subsectionId: string | undefined;
    subsection: string | undefined;
    position: number;

    constructor(data?: Partial<IAssignmentInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.assignmentNumber = _data["assignmentNumber"];
            this.cycleId = _data["cycleId"];
            this.activityId = _data["activityId"];
            this.activityType = _data["activityType"];
            this.name = _data["name"];
            this.description = _data["description"];
            this.courseSubjectId = _data["courseSubjectId"];
            this.subject = _data["subject"];
            this.releaseStatus = _data["releaseStatus"];
            this.releaseDate = _data["releaseDate"] ? new Date(_data["releaseDate"].toString()) : <any>undefined;
            this.dueDate = _data["dueDate"] ? new Date(_data["dueDate"].toString()) : <any>undefined;
            this.assignmentTypeId = _data["assignmentTypeId"];
            this.assignmentType = _data["assignmentType"];
            this.sectionId = _data["sectionId"];
            this.section = _data["section"];
            this.subsectionId = _data["subsectionId"];
            this.subsection = _data["subsection"];
            this.position = _data["position"];
        }
    }

    static fromJS(data: any): AssignmentInfo {
        data = typeof data === 'object' ? data : {};
        let result = new AssignmentInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["assignmentNumber"] = this.assignmentNumber;
        data["cycleId"] = this.cycleId;
        data["activityId"] = this.activityId;
        data["activityType"] = this.activityType;
        data["name"] = this.name;
        data["description"] = this.description;
        data["courseSubjectId"] = this.courseSubjectId;
        data["subject"] = this.subject;
        data["releaseStatus"] = this.releaseStatus;
        data["releaseDate"] = this.releaseDate ? this.releaseDate.toISOString() : <any>undefined;
        data["dueDate"] = this.dueDate ? this.dueDate.toISOString() : <any>undefined;
        data["assignmentTypeId"] = this.assignmentTypeId;
        data["assignmentType"] = this.assignmentType;
        data["sectionId"] = this.sectionId;
        data["section"] = this.section;
        data["subsectionId"] = this.subsectionId;
        data["subsection"] = this.subsection;
        data["position"] = this.position;
        return data;
    }

    clone(): AssignmentInfo {
        const json = this.toJSON();
        let result = new AssignmentInfo();
        result.init(json);
        return result;
    }
}

export interface IAssignmentInfo {
    id: string;
    assignmentNumber: string | undefined;
    cycleId: string;
    activityId: string;
    activityType: string | undefined;
    name: string | undefined;
    description: string | undefined;
    courseSubjectId: string;
    subject: string | undefined;
    releaseStatus: ReleaseStatus;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    assignmentTypeId: string;
    assignmentType: string | undefined;
    sectionId: string | undefined;
    section: string | undefined;
    subsectionId: string | undefined;
    subsection: string | undefined;
    position: number;
}

export enum AssignmentScope {
    Course = "Course",
    Students = "Students",
}

export class CourseRun implements ICourseRun {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    status: CourseRunStatus;
    startDate: Date;
    endDate: Date;
    maxEnrollments: number;
    currentEnrollments: number;
    allowLateEnrollment: boolean;
    enrollmentDeadline: Date | undefined;
    instructorNotes: string | undefined;

    constructor(data?: Partial<ICourseRun>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.isDeleted = _data["isDeleted"];
            this.creatorId = _data["creatorId"];
            this.name = _data["name"];
            this.description = _data["description"];
            this.courseId = _data["courseId"];
            this.status = _data["status"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
            this.maxEnrollments = _data["maxEnrollments"];
            this.currentEnrollments = _data["currentEnrollments"];
            this.allowLateEnrollment = _data["allowLateEnrollment"];
            this.enrollmentDeadline = _data["enrollmentDeadline"] ? new Date(_data["enrollmentDeadline"].toString()) : <any>undefined;
            this.instructorNotes = _data["instructorNotes"];
        }
    }

    static fromJS(data: any): CourseRun {
        data = typeof data === 'object' ? data : {};
        let result = new CourseRun();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["isDeleted"] = this.isDeleted;
        data["creatorId"] = this.creatorId;
        data["name"] = this.name;
        data["description"] = this.description;
        data["courseId"] = this.courseId;
        data["status"] = this.status;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        data["maxEnrollments"] = this.maxEnrollments;
        data["currentEnrollments"] = this.currentEnrollments;
        data["allowLateEnrollment"] = this.allowLateEnrollment;
        data["enrollmentDeadline"] = this.enrollmentDeadline ? this.enrollmentDeadline.toISOString() : <any>undefined;
        data["instructorNotes"] = this.instructorNotes;
        return data;
    }

    clone(): CourseRun {
        const json = this.toJSON();
        let result = new CourseRun();
        result.init(json);
        return result;
    }
}

export interface ICourseRun {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    status: CourseRunStatus;
    startDate: Date;
    endDate: Date;
    maxEnrollments: number;
    currentEnrollments: number;
    allowLateEnrollment: boolean;
    enrollmentDeadline: Date | undefined;
    instructorNotes: string | undefined;
}

export class CourseRunListDto implements ICourseRunListDto {
    id: string;
    name: string | undefined;
    status: CourseRunStatus;

    constructor(data?: Partial<ICourseRunListDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.status = _data["status"];
        }
    }

    static fromJS(data: any): CourseRunListDto {
        data = typeof data === 'object' ? data : {};
        let result = new CourseRunListDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["status"] = this.status;
        return data;
    }

    clone(): CourseRunListDto {
        const json = this.toJSON();
        let result = new CourseRunListDto();
        result.init(json);
        return result;
    }
}

export interface ICourseRunListDto {
    id: string;
    name: string | undefined;
    status: CourseRunStatus;
}

export enum CourseRunStatus {
    Draft = "Draft",
    Published = "Published",
    Active = "Active",
    Completed = "Completed",
    Cancelled = "Cancelled",
}

export class Coursework implements ICoursework {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    isTimed: boolean;
    timeLimit: number | undefined;
    attemptsAllowed: number;
    limitToPlanIds: string[] | undefined;
    previewImage: MediaItem;
    scope: AssignmentScope;
    isReleased: boolean;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    includeInTrial: boolean;
    description: string | undefined;
    activityId: string;
    position: number;
    assignmentTypeId: string | undefined;
    subsectionId: string | undefined;
    courseId: string;
    courseRunId: string;
    assignmentNumber: string | undefined;
    name: string | undefined;
    activityType: string | undefined;
    activityTypeId: string;
    releaseStatus: ReleaseStatus;
    courseSubjectId: string | undefined;

    constructor(data?: Partial<ICoursework>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.isDeleted = _data["isDeleted"];
            this.creatorId = _data["creatorId"];
            this.isTimed = _data["isTimed"];
            this.timeLimit = _data["timeLimit"];
            this.attemptsAllowed = _data["attemptsAllowed"];
            if (Array.isArray(_data["limitToPlanIds"])) {
                this.limitToPlanIds = [] as any;
                for (let item of _data["limitToPlanIds"])
                    this.limitToPlanIds!.push(item);
            }
            this.previewImage = _data["previewImage"] ? MediaItem.fromJS(_data["previewImage"]) : <any>undefined;
            this.scope = _data["scope"];
            (<any>this).isReleased = _data["isReleased"];
            this.releaseDate = _data["releaseDate"] ? new Date(_data["releaseDate"].toString()) : <any>undefined;
            this.dueDate = _data["dueDate"] ? new Date(_data["dueDate"].toString()) : <any>undefined;
            this.includeInTrial = _data["includeInTrial"];
            this.description = _data["description"];
            this.activityId = _data["activityId"];
            this.position = _data["position"];
            this.assignmentTypeId = _data["assignmentTypeId"];
            this.subsectionId = _data["subsectionId"];
            this.courseId = _data["courseId"];
            this.courseRunId = _data["courseRunId"];
            this.assignmentNumber = _data["assignmentNumber"];
            this.name = _data["name"];
            this.activityType = _data["activityType"];
            this.activityTypeId = _data["activityTypeId"];
            this.releaseStatus = _data["releaseStatus"];
            this.courseSubjectId = _data["courseSubjectId"];
        }
    }

    static fromJS(data: any): Coursework {
        data = typeof data === 'object' ? data : {};
        let result = new Coursework();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["isDeleted"] = this.isDeleted;
        data["creatorId"] = this.creatorId;
        data["isTimed"] = this.isTimed;
        data["timeLimit"] = this.timeLimit;
        data["attemptsAllowed"] = this.attemptsAllowed;
        if (Array.isArray(this.limitToPlanIds)) {
            data["limitToPlanIds"] = [];
            for (let item of this.limitToPlanIds)
                data["limitToPlanIds"].push(item);
        }
        data["previewImage"] = this.previewImage ? this.previewImage.toJSON() : <any>undefined;
        data["scope"] = this.scope;
        data["isReleased"] = this.isReleased;
        data["releaseDate"] = this.releaseDate ? this.releaseDate.toISOString() : <any>undefined;
        data["dueDate"] = this.dueDate ? this.dueDate.toISOString() : <any>undefined;
        data["includeInTrial"] = this.includeInTrial;
        data["description"] = this.description;
        data["activityId"] = this.activityId;
        data["position"] = this.position;
        data["assignmentTypeId"] = this.assignmentTypeId;
        data["subsectionId"] = this.subsectionId;
        data["courseId"] = this.courseId;
        data["courseRunId"] = this.courseRunId;
        data["assignmentNumber"] = this.assignmentNumber;
        data["name"] = this.name;
        data["activityType"] = this.activityType;
        data["activityTypeId"] = this.activityTypeId;
        data["releaseStatus"] = this.releaseStatus;
        data["courseSubjectId"] = this.courseSubjectId;
        return data;
    }

    clone(): Coursework {
        const json = this.toJSON();
        let result = new Coursework();
        result.init(json);
        return result;
    }
}

export interface ICoursework {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    isTimed: boolean;
    timeLimit: number | undefined;
    attemptsAllowed: number;
    limitToPlanIds: string[] | undefined;
    previewImage: MediaItem;
    scope: AssignmentScope;
    isReleased: boolean;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    includeInTrial: boolean;
    description: string | undefined;
    activityId: string;
    position: number;
    assignmentTypeId: string | undefined;
    subsectionId: string | undefined;
    courseId: string;
    courseRunId: string;
    assignmentNumber: string | undefined;
    name: string | undefined;
    activityType: string | undefined;
    activityTypeId: string;
    releaseStatus: ReleaseStatus;
    courseSubjectId: string | undefined;
}

export class CourseworkType implements ICourseworkType {
    id: string;
    data: any;
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    isGraded: boolean;
    icon: MediaItem;

    constructor(data?: Partial<ICourseworkType>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.data = _data["data"];
            this.name = _data["name"];
            this.description = _data["description"];
            this.courseId = _data["courseId"];
            this.isGraded = _data["isGraded"];
            this.icon = _data["icon"] ? MediaItem.fromJS(_data["icon"]) : <any>undefined;
        }
    }

    static fromJS(data: any): CourseworkType {
        data = typeof data === 'object' ? data : {};
        let result = new CourseworkType();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["data"] = this.data;
        data["name"] = this.name;
        data["description"] = this.description;
        data["courseId"] = this.courseId;
        data["isGraded"] = this.isGraded;
        data["icon"] = this.icon ? this.icon.toJSON() : <any>undefined;
        return data;
    }

    clone(): CourseworkType {
        const json = this.toJSON();
        let result = new CourseworkType();
        result.init(json);
        return result;
    }
}

export interface ICourseworkType {
    id: string;
    data: any;
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    isGraded: boolean;
    icon: MediaItem;
}

export class CreateCourseRunCommand implements ICreateCourseRunCommand {
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    startDate: Date;
    endDate: Date;

    constructor(data?: Partial<ICreateCourseRunCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"];
            this.description = _data["description"];
            this.courseId = _data["courseId"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): CreateCourseRunCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateCourseRunCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name;
        data["description"] = this.description;
        data["courseId"] = this.courseId;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        return data;
    }

    clone(): CreateCourseRunCommand {
        const json = this.toJSON();
        let result = new CreateCourseRunCommand();
        result.init(json);
        return result;
    }
}

export interface ICreateCourseRunCommand {
    name: string | undefined;
    description: string | undefined;
    courseId: string;
    startDate: Date;
    endDate: Date;
}

export class CreateCourseRunResult implements ICreateCourseRunResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    courseRunId: string;

    constructor(data?: Partial<ICreateCourseRunResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.courseRunId = _data["courseRunId"];
        }
    }

    static fromJS(data: any): CreateCourseRunResult {
        data = typeof data === 'object' ? data : {};
        let result = new CreateCourseRunResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["courseRunId"] = this.courseRunId;
        return data;
    }

    clone(): CreateCourseRunResult {
        const json = this.toJSON();
        let result = new CreateCourseRunResult();
        result.init(json);
        return result;
    }
}

export interface ICreateCourseRunResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    courseRunId: string;
}

export class CreateCourseworkCommand implements ICreateCourseworkCommand {
    cycleId: string;
    name: string | undefined;
    courseSubjectId: string;
    assignmentTypeId: string | undefined;
    subsectionId: string | undefined;
    activityType: string | undefined;
    sectionPosition: number;
    releaseDate: Date | undefined;

    constructor(data?: Partial<ICreateCourseworkCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.cycleId = _data["cycleId"];
            this.name = _data["name"];
            this.courseSubjectId = _data["courseSubjectId"];
            this.assignmentTypeId = _data["assignmentTypeId"];
            this.subsectionId = _data["subsectionId"];
            this.activityType = _data["activityType"];
            this.sectionPosition = _data["sectionPosition"];
            this.releaseDate = _data["releaseDate"] ? new Date(_data["releaseDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): CreateCourseworkCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateCourseworkCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["cycleId"] = this.cycleId;
        data["name"] = this.name;
        data["courseSubjectId"] = this.courseSubjectId;
        data["assignmentTypeId"] = this.assignmentTypeId;
        data["subsectionId"] = this.subsectionId;
        data["activityType"] = this.activityType;
        data["sectionPosition"] = this.sectionPosition;
        data["releaseDate"] = this.releaseDate ? this.releaseDate.toISOString() : <any>undefined;
        return data;
    }

    clone(): CreateCourseworkCommand {
        const json = this.toJSON();
        let result = new CreateCourseworkCommand();
        result.init(json);
        return result;
    }
}

export interface ICreateCourseworkCommand {
    cycleId: string;
    name: string | undefined;
    courseSubjectId: string;
    assignmentTypeId: string | undefined;
    subsectionId: string | undefined;
    activityType: string | undefined;
    sectionPosition: number;
    releaseDate: Date | undefined;
}

export class CreateCourseworkResult implements ICreateCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    id: string;

    constructor(data?: Partial<ICreateCourseworkResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.id = _data["id"];
        }
    }

    static fromJS(data: any): CreateCourseworkResult {
        data = typeof data === 'object' ? data : {};
        let result = new CreateCourseworkResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["id"] = this.id;
        return data;
    }

    clone(): CreateCourseworkResult {
        const json = this.toJSON();
        let result = new CreateCourseworkResult();
        result.init(json);
        return result;
    }
}

export interface ICreateCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    id: string;
}

export class CreateSegmentCommand implements ICreateSegmentCommand {
    courseRunId: string;
    name: string | undefined;
    startDate: Date;
    endDate: Date;

    constructor(data?: Partial<ICreateSegmentCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.courseRunId = _data["courseRunId"];
            this.name = _data["name"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): CreateSegmentCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateSegmentCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["courseRunId"] = this.courseRunId;
        data["name"] = this.name;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        return data;
    }

    clone(): CreateSegmentCommand {
        const json = this.toJSON();
        let result = new CreateSegmentCommand();
        result.init(json);
        return result;
    }
}

export interface ICreateSegmentCommand {
    courseRunId: string;
    name: string | undefined;
    startDate: Date;
    endDate: Date;
}

export class CreateSegmentResult implements ICreateSegmentResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;
    node: RunSegmentNode;

    constructor(data?: Partial<ICreateSegmentResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.segment = _data["segment"] ? Segment.fromJS(_data["segment"]) : <any>undefined;
            this.node = _data["node"] ? RunSegmentNode.fromJS(_data["node"]) : <any>undefined;
        }
    }

    static fromJS(data: any): CreateSegmentResult {
        data = typeof data === 'object' ? data : {};
        let result = new CreateSegmentResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["segment"] = this.segment ? this.segment.toJSON() : <any>undefined;
        data["node"] = this.node ? this.node.toJSON() : <any>undefined;
        return data;
    }

    clone(): CreateSegmentResult {
        const json = this.toJSON();
        let result = new CreateSegmentResult();
        result.init(json);
        return result;
    }
}

export interface ICreateSegmentResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;
    node: RunSegmentNode;
}

export class ErrorDto implements IErrorDto {
    code: string | undefined;
    description: string | undefined;
    type: ErrorType;

    constructor(data?: Partial<IErrorDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.description = _data["description"];
            this.type = _data["type"];
        }
    }

    static fromJS(data: any): ErrorDto {
        data = typeof data === 'object' ? data : {};
        let result = new ErrorDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["description"] = this.description;
        data["type"] = this.type;
        return data;
    }

    clone(): ErrorDto {
        const json = this.toJSON();
        let result = new ErrorDto();
        result.init(json);
        return result;
    }
}

export interface IErrorDto {
    code: string | undefined;
    description: string | undefined;
    type: ErrorType;
}

export enum ErrorType {
    None = "None",
    Failure = "Failure",
    Unexpected = "Unexpected",
    Validation = "Validation",
    Conflict = "Conflict",
    NotFound = "NotFound",
    Unauthorized = "Unauthorized",
    Forbidden = "Forbidden",
    Custom = "Custom",
}

export class GetCourseRunsResult implements IGetCourseRunsResult {
    items: CourseRunListDto[] | undefined;

    constructor(data?: Partial<IGetCourseRunsResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(CourseRunListDto.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetCourseRunsResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetCourseRunsResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): GetCourseRunsResult {
        const json = this.toJSON();
        let result = new GetCourseRunsResult();
        result.init(json);
        return result;
    }
}

export interface IGetCourseRunsResult {
    items: CourseRunListDto[] | undefined;
}

export class GetCourseworkDetailResult implements IGetCourseworkDetailResult {
    detail: Coursework;

    constructor(data?: Partial<IGetCourseworkDetailResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.detail = _data["detail"] ? Coursework.fromJS(_data["detail"]) : <any>undefined;
        }
    }

    static fromJS(data: any): GetCourseworkDetailResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetCourseworkDetailResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["detail"] = this.detail ? this.detail.toJSON() : <any>undefined;
        return data;
    }

    clone(): GetCourseworkDetailResult {
        const json = this.toJSON();
        let result = new GetCourseworkDetailResult();
        result.init(json);
        return result;
    }
}

export interface IGetCourseworkDetailResult {
    detail: Coursework;
}

export class GetCourseworkListResult implements IGetCourseworkListResult {
    items: AssignmentInfo[] | undefined;
    totalCount: number;

    constructor(data?: Partial<IGetCourseworkListResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(AssignmentInfo.fromJS(item));
            }
            this.totalCount = _data["totalCount"];
        }
    }

    static fromJS(data: any): GetCourseworkListResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetCourseworkListResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        data["totalCount"] = this.totalCount;
        return data;
    }

    clone(): GetCourseworkListResult {
        const json = this.toJSON();
        let result = new GetCourseworkListResult();
        result.init(json);
        return result;
    }
}

export interface IGetCourseworkListResult {
    items: AssignmentInfo[] | undefined;
    totalCount: number;
}

export class GetCourseworkResult implements IGetCourseworkResult {
    items: AssignmentInfo[] | undefined;

    constructor(data?: Partial<IGetCourseworkResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(AssignmentInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetCourseworkResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetCourseworkResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): GetCourseworkResult {
        const json = this.toJSON();
        let result = new GetCourseworkResult();
        result.init(json);
        return result;
    }
}

export interface IGetCourseworkResult {
    items: AssignmentInfo[] | undefined;
}

export class GetRunSegmentsResult implements IGetRunSegmentsResult {
    items: RunSegmentNode[] | undefined;

    constructor(data?: Partial<IGetRunSegmentsResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(RunSegmentNode.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetRunSegmentsResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetRunSegmentsResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): GetRunSegmentsResult {
        const json = this.toJSON();
        let result = new GetRunSegmentsResult();
        result.init(json);
        return result;
    }
}

export interface IGetRunSegmentsResult {
    items: RunSegmentNode[] | undefined;
}

export class ItemOrder implements IItemOrder {
    itemId: string;
    order: number;

    constructor(data?: Partial<IItemOrder>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.itemId = _data["itemId"];
            this.order = _data["order"];
        }
    }

    static fromJS(data: any): ItemOrder {
        data = typeof data === 'object' ? data : {};
        let result = new ItemOrder();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["itemId"] = this.itemId;
        data["order"] = this.order;
        return data;
    }

    clone(): ItemOrder {
        const json = this.toJSON();
        let result = new ItemOrder();
        result.init(json);
        return result;
    }
}

export interface IItemOrder {
    itemId: string;
    order: number;
}

export class MediaItem implements IMediaItem {
    id: string | undefined;
    url: string | undefined;

    constructor(data?: Partial<IMediaItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.url = _data["url"];
        }
    }

    static fromJS(data: any): MediaItem {
        data = typeof data === 'object' ? data : {};
        let result = new MediaItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["url"] = this.url;
        return data;
    }

    clone(): MediaItem {
        const json = this.toJSON();
        let result = new MediaItem();
        result.init(json);
        return result;
    }
}

export interface IMediaItem {
    id: string | undefined;
    url: string | undefined;
}

export class MoveCourseworkCommand implements IMoveCourseworkCommand {
    segmentId: string;
    items: ItemOrder[] | undefined;

    constructor(data?: Partial<IMoveCourseworkCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.segmentId = _data["segmentId"];
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(ItemOrder.fromJS(item));
            }
        }
    }

    static fromJS(data: any): MoveCourseworkCommand {
        data = typeof data === 'object' ? data : {};
        let result = new MoveCourseworkCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["segmentId"] = this.segmentId;
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): MoveCourseworkCommand {
        const json = this.toJSON();
        let result = new MoveCourseworkCommand();
        result.init(json);
        return result;
    }
}

export interface IMoveCourseworkCommand {
    segmentId: string;
    items: ItemOrder[] | undefined;
}

export class MoveCourseworkResult implements IMoveCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;

    constructor(data?: Partial<IMoveCourseworkResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
        }
    }

    static fromJS(data: any): MoveCourseworkResult {
        data = typeof data === 'object' ? data : {};
        let result = new MoveCourseworkResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        return data;
    }

    clone(): MoveCourseworkResult {
        const json = this.toJSON();
        let result = new MoveCourseworkResult();
        result.init(json);
        return result;
    }
}

export interface IMoveCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
}

export class MoveSegmentInput implements IMoveSegmentInput {
    courseRunId: string;
    items: ItemOrder[] | undefined;

    constructor(data?: Partial<IMoveSegmentInput>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.courseRunId = _data["courseRunId"];
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(ItemOrder.fromJS(item));
            }
        }
    }

    static fromJS(data: any): MoveSegmentInput {
        data = typeof data === 'object' ? data : {};
        let result = new MoveSegmentInput();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["courseRunId"] = this.courseRunId;
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): MoveSegmentInput {
        const json = this.toJSON();
        let result = new MoveSegmentInput();
        result.init(json);
        return result;
    }
}

export interface IMoveSegmentInput {
    courseRunId: string;
    items: ItemOrder[] | undefined;
}

export class MoveSegmentResult implements IMoveSegmentResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;

    constructor(data?: Partial<IMoveSegmentResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
        }
    }

    static fromJS(data: any): MoveSegmentResult {
        data = typeof data === 'object' ? data : {};
        let result = new MoveSegmentResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        return data;
    }

    clone(): MoveSegmentResult {
        const json = this.toJSON();
        let result = new MoveSegmentResult();
        result.init(json);
        return result;
    }
}

export interface IMoveSegmentResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
}

export class PagedResultDtoOfCourseworkType implements IPagedResultDtoOfCourseworkType {
    items: CourseworkType[] | undefined;
    totalCount: number;

    constructor(data?: Partial<IPagedResultDtoOfCourseworkType>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(CourseworkType.fromJS(item));
            }
            this.totalCount = _data["totalCount"];
        }
    }

    static fromJS(data: any): PagedResultDtoOfCourseworkType {
        data = typeof data === 'object' ? data : {};
        let result = new PagedResultDtoOfCourseworkType();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : <any>undefined);
        }
        data["totalCount"] = this.totalCount;
        return data;
    }

    clone(): PagedResultDtoOfCourseworkType {
        const json = this.toJSON();
        let result = new PagedResultDtoOfCourseworkType();
        result.init(json);
        return result;
    }
}

export interface IPagedResultDtoOfCourseworkType {
    items: CourseworkType[] | undefined;
    totalCount: number;
}

export enum ReleaseStatus {
    Draft = "Draft",
    Preview = "Preview",
    Released = "Released",
}

export class RemoteServiceErrorInfo implements IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;

    constructor(data?: Partial<IRemoteServiceErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.message = _data["message"];
            this.details = _data["details"];
            if (_data["data"]) {
                this.data = {} as any;
                for (let key in _data["data"]) {
                    if (_data["data"].hasOwnProperty(key))
                        (<any>this.data)![key] = _data["data"][key];
                }
            }
            if (Array.isArray(_data["validationErrors"])) {
                this.validationErrors = [] as any;
                for (let item of _data["validationErrors"])
                    this.validationErrors!.push(RemoteServiceValidationErrorInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): RemoteServiceErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["message"] = this.message;
        data["details"] = this.details;
        if (this.data) {
            data["data"] = {};
            for (let key in this.data) {
                if (this.data.hasOwnProperty(key))
                    (<any>data["data"])[key] = (<any>this.data)[key];
            }
        }
        if (Array.isArray(this.validationErrors)) {
            data["validationErrors"] = [];
            for (let item of this.validationErrors)
                data["validationErrors"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): RemoteServiceErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;
}

export class RemoteServiceErrorResponse implements IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;

    constructor(data?: Partial<IRemoteServiceErrorResponse>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.error = _data["error"] ? RemoteServiceErrorInfo.fromJS(_data["error"]) : <any>undefined;
        }
    }

    static fromJS(data: any): RemoteServiceErrorResponse {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        return data;
    }

    clone(): RemoteServiceErrorResponse {
        const json = this.toJSON();
        let result = new RemoteServiceErrorResponse();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;
}

export class RemoteServiceValidationErrorInfo implements IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;

    constructor(data?: Partial<IRemoteServiceValidationErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.message = _data["message"];
            if (Array.isArray(_data["members"])) {
                this.members = [] as any;
                for (let item of _data["members"])
                    this.members!.push(item);
            }
        }
    }

    static fromJS(data: any): RemoteServiceValidationErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceValidationErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["message"] = this.message;
        if (Array.isArray(this.members)) {
            data["members"] = [];
            for (let item of this.members)
                data["members"].push(item);
        }
        return data;
    }

    clone(): RemoteServiceValidationErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceValidationErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;
}

export class RemoveCourseworkCommand implements IRemoveCourseworkCommand {
    id: string;

    constructor(data?: Partial<IRemoveCourseworkCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
        }
    }

    static fromJS(data: any): RemoveCourseworkCommand {
        data = typeof data === 'object' ? data : {};
        let result = new RemoveCourseworkCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        return data;
    }

    clone(): RemoveCourseworkCommand {
        const json = this.toJSON();
        let result = new RemoveCourseworkCommand();
        result.init(json);
        return result;
    }
}

export interface IRemoveCourseworkCommand {
    id: string;
}

export class RemoveSegmentCommand implements IRemoveSegmentCommand {
    id: string;

    constructor(data?: Partial<IRemoveSegmentCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
        }
    }

    static fromJS(data: any): RemoveSegmentCommand {
        data = typeof data === 'object' ? data : {};
        let result = new RemoveSegmentCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        return data;
    }

    clone(): RemoveSegmentCommand {
        const json = this.toJSON();
        let result = new RemoveSegmentCommand();
        result.init(json);
        return result;
    }
}

export interface IRemoveSegmentCommand {
    id: string;
}

export class ResultObject implements IResultObject {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;

    constructor(data?: Partial<IResultObject>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
        }
    }

    static fromJS(data: any): ResultObject {
        data = typeof data === 'object' ? data : {};
        let result = new ResultObject();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        return data;
    }

    clone(): ResultObject {
        const json = this.toJSON();
        let result = new ResultObject();
        result.init(json);
        return result;
    }
}

export interface IResultObject {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
}

export class RunSegmentNode implements IRunSegmentNode {
    id: string;
    cycleId: string;
    nodeType: string | undefined;
    name: string | undefined;
    parentId: string | undefined;
    releaseStatus: ReleaseStatus;
    startDate: Date | undefined;
    endDate: Date | undefined;
    position: number;

    constructor(data?: Partial<IRunSegmentNode>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.cycleId = _data["cycleId"];
            this.nodeType = _data["nodeType"];
            this.name = _data["name"];
            this.parentId = _data["parentId"];
            this.releaseStatus = _data["releaseStatus"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
            this.position = _data["position"];
        }
    }

    static fromJS(data: any): RunSegmentNode {
        data = typeof data === 'object' ? data : {};
        let result = new RunSegmentNode();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["cycleId"] = this.cycleId;
        data["nodeType"] = this.nodeType;
        data["name"] = this.name;
        data["parentId"] = this.parentId;
        data["releaseStatus"] = this.releaseStatus;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        data["position"] = this.position;
        return data;
    }

    clone(): RunSegmentNode {
        const json = this.toJSON();
        let result = new RunSegmentNode();
        result.init(json);
        return result;
    }
}

export interface IRunSegmentNode {
    id: string;
    cycleId: string;
    nodeType: string | undefined;
    name: string | undefined;
    parentId: string | undefined;
    releaseStatus: ReleaseStatus;
    startDate: Date | undefined;
    endDate: Date | undefined;
    position: number;
}

export class Segment implements ISegment {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    courseRunId: string;
    name: string | undefined;
    order: number;
    releaseStatus: ReleaseStatus;
    startDate: Date;
    endDate: Date | undefined;

    constructor(data?: Partial<ISegment>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.isDeleted = _data["isDeleted"];
            this.creatorId = _data["creatorId"];
            this.courseRunId = _data["courseRunId"];
            this.name = _data["name"];
            this.order = _data["order"];
            this.releaseStatus = _data["releaseStatus"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): Segment {
        data = typeof data === 'object' ? data : {};
        let result = new Segment();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["isDeleted"] = this.isDeleted;
        data["creatorId"] = this.creatorId;
        data["courseRunId"] = this.courseRunId;
        data["name"] = this.name;
        data["order"] = this.order;
        data["releaseStatus"] = this.releaseStatus;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        return data;
    }

    clone(): Segment {
        const json = this.toJSON();
        let result = new Segment();
        result.init(json);
        return result;
    }
}

export interface ISegment {
    id: string;
    isDeleted: boolean;
    creatorId: string | undefined;
    courseRunId: string;
    name: string | undefined;
    order: number;
    releaseStatus: ReleaseStatus;
    startDate: Date;
    endDate: Date | undefined;
}

export class SubsectionDetailOutput implements ISubsectionDetailOutput {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;
    activities: AssignmentInfo[] | undefined;

    constructor(data?: Partial<ISubsectionDetailOutput>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.segment = _data["segment"] ? Segment.fromJS(_data["segment"]) : <any>undefined;
            if (Array.isArray(_data["activities"])) {
                this.activities = [] as any;
                for (let item of _data["activities"])
                    this.activities!.push(AssignmentInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): SubsectionDetailOutput {
        data = typeof data === 'object' ? data : {};
        let result = new SubsectionDetailOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["segment"] = this.segment ? this.segment.toJSON() : <any>undefined;
        if (Array.isArray(this.activities)) {
            data["activities"] = [];
            for (let item of this.activities)
                data["activities"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }

    clone(): SubsectionDetailOutput {
        const json = this.toJSON();
        let result = new SubsectionDetailOutput();
        result.init(json);
        return result;
    }
}

export interface ISubsectionDetailOutput {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;
    activities: AssignmentInfo[] | undefined;
}

export class UpdateCourseRunCommand implements IUpdateCourseRunCommand {
    courseRunId: string;
    name: string | undefined;
    description: string | undefined;
    startDate: Date;
    endDate: Date;
    maxEnrollments: number;
    allowLateEnrollment: boolean;
    enrollmentDeadline: Date | undefined;
    instructorNotes: string | undefined;

    constructor(data?: Partial<IUpdateCourseRunCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.courseRunId = _data["courseRunId"];
            this.name = _data["name"];
            this.description = _data["description"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
            this.maxEnrollments = _data["maxEnrollments"];
            this.allowLateEnrollment = _data["allowLateEnrollment"];
            this.enrollmentDeadline = _data["enrollmentDeadline"] ? new Date(_data["enrollmentDeadline"].toString()) : <any>undefined;
            this.instructorNotes = _data["instructorNotes"];
        }
    }

    static fromJS(data: any): UpdateCourseRunCommand {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateCourseRunCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["courseRunId"] = this.courseRunId;
        data["name"] = this.name;
        data["description"] = this.description;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        data["maxEnrollments"] = this.maxEnrollments;
        data["allowLateEnrollment"] = this.allowLateEnrollment;
        data["enrollmentDeadline"] = this.enrollmentDeadline ? this.enrollmentDeadline.toISOString() : <any>undefined;
        data["instructorNotes"] = this.instructorNotes;
        return data;
    }

    clone(): UpdateCourseRunCommand {
        const json = this.toJSON();
        let result = new UpdateCourseRunCommand();
        result.init(json);
        return result;
    }
}

export interface IUpdateCourseRunCommand {
    courseRunId: string;
    name: string | undefined;
    description: string | undefined;
    startDate: Date;
    endDate: Date;
    maxEnrollments: number;
    allowLateEnrollment: boolean;
    enrollmentDeadline: Date | undefined;
    instructorNotes: string | undefined;
}

export class UpdateCourseRunResult implements IUpdateCourseRunResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    courseRun: CourseRun;

    constructor(data?: Partial<IUpdateCourseRunResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.courseRun = _data["courseRun"] ? CourseRun.fromJS(_data["courseRun"]) : <any>undefined;
        }
    }

    static fromJS(data: any): UpdateCourseRunResult {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateCourseRunResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["courseRun"] = this.courseRun ? this.courseRun.toJSON() : <any>undefined;
        return data;
    }

    clone(): UpdateCourseRunResult {
        const json = this.toJSON();
        let result = new UpdateCourseRunResult();
        result.init(json);
        return result;
    }
}

export interface IUpdateCourseRunResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    courseRun: CourseRun;
}

export class UpdateCourseworkCommand implements IUpdateCourseworkCommand {
    id: string;
    name: string | undefined;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    courseSubjectId: string;
    limitToPlanIds: string[] | undefined;
    assignmentTypeId: string | undefined;
    includeInTrial: boolean;
    description: string | undefined;
    timeLimit: number | undefined;
    isTimed: boolean;
    attemptsAllowed: number;
    subsectionId: string | undefined;
    position: number;

    constructor(data?: Partial<IUpdateCourseworkCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.releaseDate = _data["releaseDate"] ? new Date(_data["releaseDate"].toString()) : <any>undefined;
            this.dueDate = _data["dueDate"] ? new Date(_data["dueDate"].toString()) : <any>undefined;
            this.courseSubjectId = _data["courseSubjectId"];
            if (Array.isArray(_data["limitToPlanIds"])) {
                this.limitToPlanIds = [] as any;
                for (let item of _data["limitToPlanIds"])
                    this.limitToPlanIds!.push(item);
            }
            this.assignmentTypeId = _data["assignmentTypeId"];
            this.includeInTrial = _data["includeInTrial"];
            this.description = _data["description"];
            this.timeLimit = _data["timeLimit"];
            this.isTimed = _data["isTimed"];
            this.attemptsAllowed = _data["attemptsAllowed"];
            this.subsectionId = _data["subsectionId"];
            this.position = _data["position"];
        }
    }

    static fromJS(data: any): UpdateCourseworkCommand {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateCourseworkCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["releaseDate"] = this.releaseDate ? this.releaseDate.toISOString() : <any>undefined;
        data["dueDate"] = this.dueDate ? this.dueDate.toISOString() : <any>undefined;
        data["courseSubjectId"] = this.courseSubjectId;
        if (Array.isArray(this.limitToPlanIds)) {
            data["limitToPlanIds"] = [];
            for (let item of this.limitToPlanIds)
                data["limitToPlanIds"].push(item);
        }
        data["assignmentTypeId"] = this.assignmentTypeId;
        data["includeInTrial"] = this.includeInTrial;
        data["description"] = this.description;
        data["timeLimit"] = this.timeLimit;
        data["isTimed"] = this.isTimed;
        data["attemptsAllowed"] = this.attemptsAllowed;
        data["subsectionId"] = this.subsectionId;
        data["position"] = this.position;
        return data;
    }

    clone(): UpdateCourseworkCommand {
        const json = this.toJSON();
        let result = new UpdateCourseworkCommand();
        result.init(json);
        return result;
    }
}

export interface IUpdateCourseworkCommand {
    id: string;
    name: string | undefined;
    releaseDate: Date | undefined;
    dueDate: Date | undefined;
    courseSubjectId: string;
    limitToPlanIds: string[] | undefined;
    assignmentTypeId: string | undefined;
    includeInTrial: boolean;
    description: string | undefined;
    timeLimit: number | undefined;
    isTimed: boolean;
    attemptsAllowed: number;
    subsectionId: string | undefined;
    position: number;
}

export class UpdateCourseworkResult implements IUpdateCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    id: string;

    constructor(data?: Partial<IUpdateCourseworkResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.id = _data["id"];
        }
    }

    static fromJS(data: any): UpdateCourseworkResult {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateCourseworkResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["id"] = this.id;
        return data;
    }

    clone(): UpdateCourseworkResult {
        const json = this.toJSON();
        let result = new UpdateCourseworkResult();
        result.init(json);
        return result;
    }
}

export interface IUpdateCourseworkResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    id: string;
}

export class UpdateSectionCommand implements IUpdateSectionCommand {
    id: string;
    name: string | undefined;
    startDate: Date;
    endDate: Date;
    releaseStatus: ReleaseStatus;

    constructor(data?: Partial<IUpdateSectionCommand>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.startDate = _data["startDate"] ? new Date(_data["startDate"].toString()) : <any>undefined;
            this.endDate = _data["endDate"] ? new Date(_data["endDate"].toString()) : <any>undefined;
            this.releaseStatus = _data["releaseStatus"];
        }
    }

    static fromJS(data: any): UpdateSectionCommand {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateSectionCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["startDate"] = this.startDate ? formatDate(this.startDate) : <any>undefined;
        data["endDate"] = this.endDate ? formatDate(this.endDate) : <any>undefined;
        data["releaseStatus"] = this.releaseStatus;
        return data;
    }

    clone(): UpdateSectionCommand {
        const json = this.toJSON();
        let result = new UpdateSectionCommand();
        result.init(json);
        return result;
    }
}

export interface IUpdateSectionCommand {
    id: string;
    name: string | undefined;
    startDate: Date;
    endDate: Date;
    releaseStatus: ReleaseStatus;
}

export class UpdateSectionResult implements IUpdateSectionResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;

    constructor(data?: Partial<IUpdateSectionResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).isError = _data["isError"];
            (<any>this).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : <any>undefined;
            (<any>this).errorMessage = _data["errorMessage"];
            this.segment = _data["segment"] ? Segment.fromJS(_data["segment"]) : <any>undefined;
        }
    }

    static fromJS(data: any): UpdateSectionResult {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateSectionResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        data["errorMessage"] = this.errorMessage;
        data["segment"] = this.segment ? this.segment.toJSON() : <any>undefined;
        return data;
    }

    clone(): UpdateSectionResult {
        const json = this.toJSON();
        let result = new UpdateSectionResult();
        result.init(json);
        return result;
    }
}

export interface IUpdateSectionResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    segment: Segment;
}

function formatDate(d: Date) {
    return d.getFullYear() + '-' + 
        (d.getMonth() < 9 ? ('0' + (d.getMonth()+1)) : (d.getMonth()+1)) + '-' +
        (d.getDate() < 10 ? ('0' + d.getDate()) : d.getDate());
}

export class ApiException extends Error {
    message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}