﻿namespace Tec.Ecademy.Academics.Domain;

public class Segment: DocEntity
{
    public Segment(Guid id) : base(id){}

    public Guid CourseRunId { get; set; }

    public string Name { get; set; }

    /// <summary>
    /// Orders section in a run
    /// </summary>
    public int Order { get; set; }

    public ReleaseStatus ReleaseStatus { get; set; }

    public DateOnly StartDate { get; set; }

    public DateOnly? EndDate { get; set; }

    
}