﻿using Tec.Rad.Data;

namespace Tec.Ecademy.Academics.ReadModel;

/// <summary>
/// Coursework that user has access to
/// </summary>
public class LearnerAssignment : ISqlView
{
    public Guid UserId { get; set; }

    public Guid AssignmentId { get; set; }

    public Guid CycleId { get; set; }

    public Guid? ActivityResultId { get; set; }

    public string ActivityNumber { get; set; }

    public string Name { get; set; }

    public string ActivityType { get; set; }

    public Guid SubjectId { get; init; }
    
    public string Subject { get; set; }

    public Guid AssignmentTypeId { get; init; }
    
    public string AssignmentType { get; init; }

    public AssignmentStatus Status { get; set; } = AssignmentStatus.Due;

}