﻿using Tec.Ecademy.Content.Activities;
using Tec.Ecademy.Content.Infra.Data;
using Tec.Rad.Extensions.Data.Marten;
using Tec.Rad.Modular;
using Volo.Abp.Modularity;

namespace Tec.Ecademy.Content.Infra;

[DependsOn(
    typeof(RadMartenModule),
    typeof(ContentActivitiesModule),
    typeof(ContentModule)
)]
public class ContentInfraModule: RadModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
       this.RegisterDocDb<ContentDb>();
            
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
     

    }
}