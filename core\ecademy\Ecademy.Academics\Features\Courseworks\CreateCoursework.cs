﻿using Tec.Rad.Apis;
using Tec.Rad.Features.Utils;

namespace Tec.Ecademy.Academics.Features.Courseworks;

public static class CreateCoursework
{
    public class Endpoint : CommandEndpoint<Command, Result>
    {

    }

    public record Command : CommandRecord<Result>
    {
        public Guid CycleId { get; init; }
        public string Name { get; init; }
        public Guid CourseSubjectId { get; init; }
        public Guid? AssignmentTypeId { get; init; }
        public Guid? SubsectionId { get; init; }
        public string ActivityType { get; init; }
        public int SectionPosition { get; init; }
        public DateTime? ReleaseDate { get; init; }
    }

    public class Result : ResultObject
    {
        public Guid Id { get; set; }
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.Name).NotEmpty().MaximumLength(200);
            RuleFor(x => x.CycleId).NotEmpty();
            RuleFor(x => x.CourseSubjectId).NotEmpty();
            RuleFor(x => x.ActivityType).NotEmpty();
        }
    }


    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        private readonly INumberGenerator _numberGenerator;

        public Handler(INumberGenerator numberGenerator)
        {
            _numberGenerator = numberGenerator;
        }

        protected override async Task Execute(CancellationToken cancellationToken)
        {
            var number = await GenerateNumber(Input.CycleId);
            var assignment = new Coursework(NewId())
            {
                AssignmentNumber = number,
                CourseRunId = Input.CycleId,
                Name = Input.Name,
                ReleaseDate = Input.ReleaseDate,
                CourseSubjectId = Input.CourseSubjectId,
                SubsectionId = Input.SubsectionId,
                Position = Input.SectionPosition,
                ActivityType = Input.ActivityType,
                ActivityId = NewId(),
                AssignmentTypeId = Input.AssignmentTypeId
            };

            await DocDb.InsertAsync(assignment);
            Output.Id = assignment.Id;
        }

        private async Task<string> GenerateNumber(Guid cycleId)
        {
            var prefix = "ACT";
            var number = await _numberGenerator.NextNumber($"CourseItem-{cycleId}", 1000);
            return $"{prefix}{number}";
        }
    }
}