using FluentValidation;
using Tec.Ecademy.Content.Domain;
using Tec.Rad.Apis;
using Tec.Rad.Cqs;

namespace Tec.Ecademy.Content.Features.Courses;

public static class CreateCourse
{
    public record Command : CommandRecord<Result>
    {
        public string Name { get; init; }
        public string Description { get; init; }
        public Guid GradeId { get; init; }
    }

    public class Result : ResultObject
    {
        public Guid Id { get; set; }
    }
    
    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.Name).NotEmpty();
            RuleFor(x => x.GradeId).NotEmpty();
        }
    }

    
    

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            var course = new Course()
            {
                Id = NewId(),
                Name = Input.Name,
                Description = Input.Description,
                GradeId = Input.GradeId
            };

            await DocDb.InsertAsync(course);
            //await DocDb.SaveChangesAsync();
            
            Output.Id = course.Id;
            
        }
    }

    public class Endpoint : CommandEndpoint<Command,Result> {

    }

}