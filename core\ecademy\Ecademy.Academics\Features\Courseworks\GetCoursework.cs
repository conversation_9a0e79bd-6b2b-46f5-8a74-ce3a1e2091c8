﻿using Tec.Ecademy.Academics.ReadModel;
using Tec.Rad.Apis;
using Tec.Rad.SqlKata;
using Volo.Abp.Application.Dtos;

namespace Tec.Ecademy.Academics.Features.Courseworks;

public static class GetCoursework
{
    public class Endpoint : GetEndpoint<Query, Result>
    {

    }

    public class Query : IQueryRequest<Result>
    {
        
        public Guid SubsectionId { get; set; }
    }

    public class Result : ListResultDto<AssignmentInfo>
    {

    }

    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Items = await Db.ExpressionQuery<AssignmentInfo>()
                .Where(a =>  a.SubsectionId, Input.SubsectionId)
                .ToListAsync();
        }
    }
}