﻿using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Courseworks;

public static class RemoveCoursework
{
    public class Endpoint : CommandEndpoint<Command, ResultObject>
    {

    }

    public class Command : CommandRequest<ResultObject>
    {
        public Guid Id { get; set; }
    }

    internal class Handler : DocDbCommandExecutor<Command, ResultObject>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            DocDb.Session.Delete<Domain.Coursework>(Input.Id);
        }
    }
}