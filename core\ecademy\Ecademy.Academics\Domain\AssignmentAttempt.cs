﻿using Ecademy.Common.Common;
using Ecademy.Common.Core;
using Tec.Rad.Annotations;
using Tec.Rad.Utils;

namespace Tec.Ecademy.Academics.Domain;

/// <summary>
/// Created each time a learner attempts an  Assignment
/// </summary>
/// <remarks>
/// An assignment can be attempted one or multiple times depending on the MaxAttempt setting of the Assignment
///
/// </remarks>
public class AssignmentAttempt: DocEntity
{
    private List<StatusHistory> _statusHistory;


    public Guid Id { get; set; }

    public AssignmentAttempt(Assignment submission)
    {
        this.Id = GuidGenerator.New();

    }

        
    public int AttemptNumber { get; set; }

    public AttemptStatus Status { get; private set; }

    public bool IsLate { get; set; }

    public bool IsGraded { get; set; }

    public bool IsCompleted { get; set; }


    [JsonData]
    public GradeResult Grade { get; private set; }

    public string Feedback { get; set; }

    public DateTime? StartTime { get; set; }

    public DateTime? SubmittedTime { get; set; }

    [JsonData]
    public List<StatusHistory> StatusHistory
    {
        get => _statusHistory ??= new List<StatusHistory>();
        set => _statusHistory = value;
    }

    public void AssignGrade(GradeResult grade, DateTime timeStamp)
    {
        Grade = grade;
        ChangeStatus(AttemptStatus.Graded, timeStamp);
    }

    public void ChangeStatus(AttemptStatus status, DateTime timeStamp)
    {
        Status = status;
        StatusHistory.Add(new StatusHistory(status.ToString(), timeStamp));

    }





}