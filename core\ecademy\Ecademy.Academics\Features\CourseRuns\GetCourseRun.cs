﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Tec.Rad.Apis;
using Tec.Rad.Cqs;

namespace Tec.Ecademy.Academics.Features.CourseRuns;

public static class GetCourseRun
{



    public record Query : IQueryRequest<Result>
    {
        public Guid CourseRunId { get; set; }
    }

    public class Result
    {
        public CourseRun Run { get; set; }
    }

    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Run = await DocDb.GetAsync<CourseRun>(Input.CourseRunId);
        }
    }

    public class Endpoint : GetEndpoint<Query, Result>
    {
        
    }



}