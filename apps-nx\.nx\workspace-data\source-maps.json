{"a-ecademy/pep/ed-pep-activity": {"root": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "name": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "tags": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "targets": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/pep/ed-pep-activity/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "release": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/pep/ed-pep-activity/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/pep/ed-pep-guide": {"root": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "name": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "tags": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "targets": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/pep/ed-pep-guide/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "release": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/pep/ed-pep-guide/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/pep/ed-pep-shared": {"root": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "name": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "tags": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "targets": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/pep/ed-pep-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "release": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/pep/ed-pep-shared/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/pep/ed-pep-ui": {"root": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "name": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "tags": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "targets": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/pep/ed-pep-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "release": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/pep/ed-pep-ui/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/shared/ed-shared": {"root": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "name": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "tags": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "targets": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/shared/ed-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "release": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/shared/ed-shared/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/studio/ed-studio-admin": {"root": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "name": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "tags": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "targets": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/studio/ed-studio-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "release": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/studio/ed-studio-admin/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/studio/ed-studio-content": {"root": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "name": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "tags": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "targets": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/studio/ed-studio-content/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "release": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/studio/ed-studio-content/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/studio/ed-studio-shared": {"root": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "name": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "tags": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "tags.npm:public": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "metadata.js": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "targets": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-ecademy/studio/ed-studio-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "release": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.test": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.test.executor": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.test.options": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.lint": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-ecademy/studio/ed-studio-shared/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-protrac/pro-admin": {"root": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "name": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "tags": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "tags.npm:public": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "metadata.js": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "targets": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-protrac/pro-admin/package.json", "nx/core/package-json"], "$schema": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "sourceRoot": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "prefix": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "projectType": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.executor": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.options": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.test": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.test.executor": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.test.options": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.lint": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-protrac/pro-admin/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-protrac/pro-shared": {"root": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "name": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "tags": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "tags.npm:public": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "metadata.js": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "targets": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-protrac/pro-shared/package.json", "nx/core/package-json"], "$schema": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "sourceRoot": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "prefix": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "projectType": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.executor": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.options": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.test": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.test.executor": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.test.options": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.lint": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-protrac/pro-shared/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@rad-app": {"root": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "tags.npm:public": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-tec/@rad-app/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "prefix": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test.executor": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test.options": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test.options.tsConfig": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-tec/@rad-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@rad-core": {"root": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "tags.npm:public": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-tec/@rad-core/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "prefix": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test.executor": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test.options": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test.options.tsConfig": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-tec/@rad-core/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@rad-infra": {"root": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "tags.npm:public": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-tec/@rad-infra/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "prefix": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.test": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.test.executor": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.test.options": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.lint": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.lint.executor": ["a-tec/@rad-infra/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@rad-nx": {"root": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "tags.npm:private": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-nx/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.options.main": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["a-tec/@rad-nx/project.json", "nx/core/project-json"], "targets.build.options.assets": ["a-tec/@rad-nx/project.json", "nx/core/project-json"]}, "a-tec/@rad-ui": {"root": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "tags.npm:public": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-tec/@rad-ui/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "prefix": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test.executor": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test.options": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test.options.tsConfig": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-tec/@rad-ui/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@rad-xui": {"root": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "name": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "tags": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "tags.npm:public": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "metadata.js": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "targets": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["a-tec/@rad-xui/package.json", "nx/core/package-json"], "$schema": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "prefix": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "projectType": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.options.project": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.configurations.production.tsConfig": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.configurations.development.tsConfig": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test.executor": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test.outputs": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test.options": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test.options.tsConfig": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["a-tec/@rad-xui/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, ".": {"root": ["project.json", "nx/core/project-json"], "name": ["project.json", "nx/core/project-json"], "includedScripts": ["package.json", "nx/core/package-json"], "tags": ["package.json", "nx/core/package-json"], "tags.npm:private": ["package.json", "nx/core/package-json"], "metadata.targetGroups": ["package.json", "nx/core/package-json"], "metadata.js": ["package.json", "nx/core/package-json"], "metadata.js.packageName": ["package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["package.json", "nx/core/package-json"], "targets": ["package.json", "nx/core/package-json"], "$schema": ["project.json", "nx/core/project-json"], "targets.local-registry": ["project.json", "nx/core/project-json"], "targets.local-registry.executor": ["project.json", "nx/core/project-json"], "targets.local-registry.options": ["project.json", "nx/core/project-json"], "targets.local-registry.options.port": ["project.json", "nx/core/project-json"], "targets.local-registry.options.config": ["project.json", "nx/core/project-json"], "targets.local-registry.options.storage": ["project.json", "nx/core/project-json"]}, "a-ecademy/x-pep-app": {"root": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "name": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "tags": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.index": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.browser": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.assets": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.styles": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.executor": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.configurations": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.options": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.options.port": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.extract-i18n": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["a-ecademy/x-pep-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "a-ecademy/x-studio-app": {"root": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "name": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "$schema": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "projectType": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "prefix": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "sourceRoot": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "tags": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.executor": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.index": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.browser": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.assets": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.styles": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.executor": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.configurations": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.options": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.options.port": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.extract-i18n": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["a-ecademy/x-studio-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "a-protrac/x-protrac-app": {"root": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "name": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "$schema": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "projectType": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "prefix": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "sourceRoot": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "tags": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.executor": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.index": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.browser": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.assets": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.styles": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.executor": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.configurations": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.options": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.options.port": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.extract-i18n": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["a-protrac/x-protrac-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "a-tec/@template-app": {"root": ["a-tec/@template-app/project.json", "nx/core/project-json"], "name": ["a-tec/@template-app/project.json", "nx/core/project-json"], "$schema": ["a-tec/@template-app/project.json", "nx/core/project-json"], "projectType": ["a-tec/@template-app/project.json", "nx/core/project-json"], "prefix": ["a-tec/@template-app/project.json", "nx/core/project-json"], "sourceRoot": ["a-tec/@template-app/project.json", "nx/core/project-json"], "tags": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.executor": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.index": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.browser": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.assets": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.styles": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.executor": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.configurations": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.options": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.options.port": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.extract-i18n": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["a-tec/@template-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/tec-app": {"root": ["apps/tec-app/project.json", "nx/core/project-json"], "name": ["apps/tec-app/project.json", "nx/core/project-json"], "$schema": ["apps/tec-app/project.json", "nx/core/project-json"], "projectType": ["apps/tec-app/project.json", "nx/core/project-json"], "prefix": ["apps/tec-app/project.json", "nx/core/project-json"], "sourceRoot": ["apps/tec-app/project.json", "nx/core/project-json"], "tags": ["apps/tec-app/project.json", "nx/core/project-json"], "targets": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.outputs": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.browser": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.assets": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.options.styles": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.continuous": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.configurations": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.extract-i18n": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.lint": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.test": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.test.outputs": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.test.options": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.continuous": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.executor": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.options": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.options.buildTarget": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.options.staticFilePath": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.serve-static.options.spa": ["apps/tec-app/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}}