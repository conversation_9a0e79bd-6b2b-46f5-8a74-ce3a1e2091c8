﻿using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.CourseRuns;

public static class UpdateCourseRun
{
    public record Command : ICommandRequest<Result>
    {
        public required Guid CourseRunId { get; init; }
        public required string Name { get; init; }
        public string? Description { get; init; }
        public required DateOnly StartDate { get; init; }
        public required DateOnly EndDate { get; init; }
        public int MaxEnrollments { get; init; }
        public bool AllowLateEnrollment { get; init; }
        public DateTimeOffset? EnrollmentDeadline { get; init; }
        public string? InstructorNotes { get; init; }
    }

    public class Result : ResultObject
    {
        public CourseRun CourseRun { get; set; }
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {

        }
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            Logger.LogInformation("Updating course run {CourseRunId} by user {UserId}", 
                Input.CourseRunId, CurrentUser.Id);
                
            // Load existing course run
            var courseRun = await DocDb.GetAsync<CourseRun>(Input.CourseRunId);
            if (courseRun == null)
            {
                Logger.LogWarning("Attempted to update non-existent course run {CourseRunId}", Input.CourseRunId);
                Output.NotFound("Course run not found", "CourseRunNotFound");
                return;
            }
            
            if(await DocDb.Session.HasOverlappingCourseRuns(courseRun.CourseId, Input.StartDate, Input.EndDate))
            {
                Output.Conflict("Course run overlaps with existing course run", "OverlappingCourseRuns");
                return;
            }
            
            courseRun.Name = Input.Name.Trim();
            courseRun.Description = Input.Description?.Trim() ?? string.Empty;
            courseRun.StartDate = Input.StartDate;
            courseRun.EndDate = Input.EndDate;
            courseRun.MaxEnrollments = Input.MaxEnrollments;
            courseRun.AllowLateEnrollment = Input.AllowLateEnrollment;
            courseRun.EnrollmentDeadline = Input.EnrollmentDeadline;
            courseRun.InstructorNotes = Input.InstructorNotes?.Trim();
            
            DocDb.Update(courseRun);
            Output.CourseRun = courseRun;
        }
    }

    [Authorize(AcademicsPermissions.CourseRuns.Edit)]
    public class Endpoint : CommandEndpoint<Command, Result>
    {

    }

}