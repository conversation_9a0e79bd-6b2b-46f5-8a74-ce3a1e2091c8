import { computed, inject, Injectable, resource } from "@angular/core";
import { ScheduleApiProxy, CreateSegmentCommand } from "@ed/share/data-academics";
import { apiResultFrom, executeApi, injectRouteParam } from "@tec/rad-core/utils";
import { RadFormConfig } from "@tec/rad-xui/form";
import { CommandService, RadSignalStore } from "@tec/rad-xui/services";

@Injectable()
export class ScheduleFacade extends RadSignalStore {

    #scheduleApi = inject(ScheduleApiProxy);
    #commands = inject(CommandService);
    private courseRunId = injectRouteParam("courseRunId");
    

    constructor() {
        super();
    }

    private _segments = resource({
        params: ()=> this.courseRunId,
        loader: (params) =>{
            return executeApi(this.#scheduleApi.getRunSegments(params.params))
        } 
    })  

    segments = computed(() => {
        const items = this._segments.value()?.items;
        // Sort by position as requested
        return items?.sort((a, b) => a.position - b.position);
    });
    loading = computed(() => this._segments.isLoading());



    private createSegmentFields: RadFormConfig = [
        { key: 'name', label: 'Name', type: 'input', rowNum: 1, required: true},
        { key: 'startDate', label: 'Start Date', type: 'datepicker', rowNum: 2, required: true},
        { key: 'endDate', label: 'End Date', type: 'datepicker', rowNum: 2, required: true},
    ]


    async createSegment(){
        const cmd = new CreateSegmentCommand();
        cmd.courseRunId = this.courseRunId;
        await this.#commands.openForm(cmd, this.createSegmentFields, "Create Segment",

            async (data)=> {
                const result = await apiResultFrom(this.#scheduleApi.createSegment(data));
                if(result.isSuccess) {
                    this._segments.reload();
                    return true;
                }
                return false;
                
            }
        );
        this._segments.reload();
    }

}
