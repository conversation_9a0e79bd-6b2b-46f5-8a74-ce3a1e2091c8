﻿--Sql for LearnerAssignment
select e.user_id, a.assignment_id, a.cycle_id, r.learner_submission_id,
a.assignment_number, a.name, a.activity_type, a.course_subject_id, s.subject_id, s.name subject,
a.assignment_type_id, ty.name assignment_type,
coalesce(r.status, 'Due') status
        from learn.assignment a
inner join learn.course_enrollment e on a.cycle_id = e.cycle_id and a.is_deleted = false and e.is_deleted = false
left join learn.learner_submission r on a.assignment_id = r.assignment_id and e.user_id = r.user_id
left join learn.course_cycle ro on ro.run_outline_id = a.cycle_id
left join learn.course_subject s on a.course_subject_id = s.course_subject_id
left join learn.assignment_type ty on a.assignment_type_id = ty.assignment_type_id

where
(jsonb_array_length(a.data->'LimitToPlanIds') = 0
or
COALESCE(a.data->'LimitToPlanIds' @> e.plan_id::text::jsonb, true)
);
