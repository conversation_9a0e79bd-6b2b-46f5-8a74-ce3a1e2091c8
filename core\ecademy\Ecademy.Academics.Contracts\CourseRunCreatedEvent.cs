﻿using Tec.Rad.Contracts;

namespace Tec.Ecademy.Academics.Contracts;

public class CourseRunCreatedEvent : IDomainEvent
{
    public Guid CourseRunId { get; set; }
    public Guid CourseId { get; set; }
    public string Name { get; set; }
    public DateTimeOffset StartDate { get; set; }
    public DateTimeOffset EndDate { get; set; }
    public int MaxEnrollments { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTimeOffset OccurredOn { get; set; } = DateTimeOffset.UtcNow;
}