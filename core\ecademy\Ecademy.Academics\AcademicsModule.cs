﻿using Tec.Rad.Apis;
using Tec.Rad.Data;
using Tec.Rad.Modular;
using Volo.Abp.Modularity;

namespace Tec.Ecademy.Academics;

[DbSchema(AcademicsConstants.DbSchema)]
public class AcademicsModule: RadModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<RadApiOptions>(options =>
        {
            options.Register<AcademicsModule>("academics","Academics","academics")
            .AddSliceControllers("Features");

        });

    }
}