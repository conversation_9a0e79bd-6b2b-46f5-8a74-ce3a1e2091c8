﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Tec.Rad.Apis;
using Tec.Rad.Cqs;
using Tec.Rad.SqlKata;

namespace Tec.Ecademy.Academics.Features.CourseRuns;

/// <summary>
/// Get course runs for a course
/// </summary>
public static class GetCourseRuns
{

    public record Query : IQueryRequest<Result>
    {
        public Guid CourseId { get; set; }
    }

    public class Result
    {
        public List<CourseRunListDto> Items { get; set; } = new();
    }

    public class CourseRunListDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public CourseRunStatus Status { get; set; }
        public string Year { get; set; }
        public int YearNumber { get; set; }
        public DateOnly StartDate { get; set; }
        public DateOnly EndDate { get; set; }
        
    }

    private const string Sql = @"
    select a.id, a.name, a.status, a.start_date, a.end_date,
    b.name as year, b.year_number as year_number
    from academics.xr_course_run_dv a
    join academics.xr_school_year_dv b on a.year_id = b.id
    where a.course_id = @CourseId
    ";

    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            var runs = await Db.SelectAsync<CourseRunListDto>(Sql, new { CourseId = Input.CourseId });

            Output.Items = runs.ToList();
        }
    }

    public class Endpoint : GetEndpoint<Query, Result>
    {
        

    }



}