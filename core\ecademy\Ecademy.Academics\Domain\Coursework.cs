﻿using Ecademy.Common.Core.Media;

namespace Tec.Ecademy.Academics.Domain;

/// <summary>
/// Teacher-authored work item (the template/config).
/// One Coursework is assigned to many students and produces many Assignments.
/// </summary>
public class Coursework : DocEntity, IName
{
    public bool IsTimed { get; set; }

    /// <summary>
    /// Defines the time limit in minutes for timed activities
    /// </summary>
    public int? TimeLimit { get; set; }

    /// <summary>
    /// Defines the number of times an activity can be attempted
    /// </summary>
    public int AttemptsAllowed { get; set; } = 1;

    public List<Guid> LimitToPlanIds { get; set; } = new List<Guid>();
    public MediaItem PreviewImage { get; set; } = new MediaItem();
    public AssignmentScope Scope { get; set; } = AssignmentScope.Course;

    /// <summary>
    /// Only released coursework will be available to students
    /// </summary>
    public bool IsReleased { get; private set; }

    /// <summary>
    /// Date and time activity will be released to learners.
    /// </summary>
    public DateTime? ReleaseDate { get; set; }

    /// <summary>
    /// When due date is set, assignments submitted after due date will not be marked
    /// </summary>
    public DateTime? DueDate { get; set; }

    public bool IncludeInTrial { get; set; }
    public string Description { get; set; }

    /// <summary>
    /// Id of item for activity
    /// </summary>
    public Guid ActivityId { get; set; }

    /// <summary>
    /// Determines order of activity in subsection
    /// </summary>
    public int Position { get; set; }

    public Guid? AssignmentTypeId { get; set; }
    public Guid? SubsectionId { get; set; }

    public Coursework(Guid id) : base(id)
    {
    }

    public Guid CourseId { get; set; }
    public Guid CourseRunId { get; set; }

    /// <summary>
    /// Number used to easily identify activities, automatically generated
    /// </summary>
    public string AssignmentNumber { get; init; }

    public string Name { get; set; }

    /// <summary>
    /// Type of activity
    /// </summary>
    public string ActivityType { get; set; }

    public Guid ActivityTypeId { get; set; }
    public ReleaseStatus ReleaseStatus { get; private set; }
    public Guid? CourseSubjectId { get; set; }

    public override string ToString()
    {
        return Name;
    }
}