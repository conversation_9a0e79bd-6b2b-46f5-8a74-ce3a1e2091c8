# Ecademy NSwag Generation Server

A lightweight ASP.NET Core application designed specifically for fast Angular service proxy generation using NSwag.

## Purpose

This server solves the problem of slow startup times when generating API proxies during development. Instead of starting the full Ecademy application with database connections, authentication, and all services, this lightweight server only loads what's necessary for API discovery and Swagger generation.

## Features

- ⚡ **Fast Startup**: Starts in seconds instead of minutes
- 🎯 **API-Only**: Only loads API controllers and Swagger generation
- 🚫 **No Heavy Services**: Skips database, authentication, caching, etc.
- 📝 **Multi-Module Support**: Generates proxies for Content, Academics, and Platform modules
- 🔄 **Development Optimized**: Perfect for rapid development cycles

## Architecture

### What's Included
- ABP Core MVC framework
- Swagger/OpenAPI generation
- API controller discovery
- Module registration (Content, Academics, Platform)

### What's Excluded
- Database connections (Marten, EF Core)
- Authentication/Authorization
- Caching services
- Background jobs
- Email services
- File storage
- Logging infrastructure
- Multi-tenancy

## Usage

### Option 1: Manual Server Start
```bash
# Navigate to the project directory
cd core/ecademy/Ecademy.Sys.NSwagGen

# Start the server
dotnet run

# Or use the batch file
start-nswag-server.bat
```

### Option 2: Automated Generation (Recommended)
```bash
# From the apps-nx directory
npm run nswag:ecademy:fast
```

This will:
1. Automatically start the lightweight server
2. Wait for it to be ready
3. Generate all API proxies
4. Stop the server
5. Clean up

### Option 3: Traditional Method (Slower)
```bash
# Uses the full application server
npm run nswag:ecademy
```

## Server Endpoints

- **Base URL**: `http://localhost:5555`
- **Swagger UI**: `http://localhost:5555/swagger`
- **Content API**: `http://localhost:5555/swagger/content/swagger.json`
- **Academics API**: `http://localhost:5555/swagger/academics/swagger.json`
- **Platform API**: `http://localhost:5555/swagger/platform/swagger.json`

## Development Workflow

### For API Changes
1. Make changes to your API controllers/features
2. Run `npm run nswag:ecademy:fast` to regenerate proxies
3. Use the updated TypeScript clients in your Angular app

### For New Modules
1. Add the new module to `EcademyNSwagModule.cs`
2. Update the proxy configuration in `ecademy-nswag-fast.js`
3. Regenerate proxies

## Performance Comparison

| Method | Startup Time | Use Case |
|--------|-------------|----------|
| Full Application | 30-60 seconds | Production, full testing |
| Lightweight Server | 3-5 seconds | API proxy generation |
| Automated Script | 5-8 seconds | Development workflow |

## Configuration

### Port Configuration
The server runs on port `5555` by default. To change:

1. Update `appsettings.json`:
```json
{
  "Urls": "http://localhost:YOUR_PORT"
}
```

2. Update `ecademy-nswag-fast.js`:
```javascript
const config = {
  url: "localhost:YOUR_PORT",
  // ...
}
```

### Adding New Modules
To include additional modules in proxy generation:

1. Add module dependency to `EcademyNSwagModule.cs`:
```csharp
[DependsOn(
    // ... existing modules
    typeof(YourNewModule)
)]
```

2. Register the module API:
```csharp
options.Register<YourNewModule>("your-api", "Your API", "your-api")
    .AddSliceControllers("Features");
```

3. Add proxy configuration:
```javascript
{ api:"your-api", output: "path/to/your-api-client.ts"}
```

## Troubleshooting

### Server Won't Start
- Check if port 5555 is already in use
- Ensure all project references are correct
- Verify .NET SDK is installed

### Proxy Generation Fails
- Ensure the server is running and accessible
- Check the Swagger endpoints are available
- Verify NSwag is installed: `npm list nswag`

### Missing APIs
- Ensure the module is properly registered in `EcademyNSwagModule`
- Check that controllers are in the correct namespace
- Verify the module's API registration

## Files Structure

```
Ecademy.Sys.NSwagGen/
├── EcademyNSwagModule.cs      # Main ABP module
├── Program.cs                 # Application entry point
├── appsettings.json          # Configuration
├── start-nswag-server.bat    # Quick start script
├── Properties/
│   └── launchSettings.json   # Development settings
└── README.md                 # This file
```

## Benefits

1. **Faster Development**: Reduce proxy generation time from minutes to seconds
2. **Resource Efficient**: Uses minimal system resources
3. **Reliable**: No database dependencies means fewer failure points
4. **Maintainable**: Simple, focused codebase
5. **Scalable**: Easy to add new modules as needed

This lightweight approach significantly improves the developer experience when working with API changes during frontend development.