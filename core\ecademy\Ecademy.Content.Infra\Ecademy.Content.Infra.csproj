﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\common.props"></Import>
 

	<ItemGroup>
	  <ProjectReference Include="..\..\tec\Tec.Rad.Extensions\Tec.Rad.Extensions.csproj" />
	  <ProjectReference Include="..\..\tec\Tec.Rad.Extensions.Data\Tec.Rad.Extensions.Data.csproj" />
	  <ProjectReference Include="..\Ecademy.Content.Activities\Ecademy.Content.Activities.csproj" />
	  <ProjectReference Include="..\Ecademy.Content\Ecademy.Content.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AWSSDK.S3" />

		<PackageReference Include="Microsoft.EntityFrameworkCore.Design">
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	    <PrivateAssets>all</PrivateAssets>
	  </PackageReference>
	</ItemGroup>

</Project>
