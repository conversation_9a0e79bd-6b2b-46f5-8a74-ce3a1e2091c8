import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input } from '@angular/core';
import { RadPage } from '@tec/rad-ui/layout';
import { ScheduleFacade } from './schedule-store';
import { RadEjGrid } from '@tec/rad-xui/ej-grid';
import { RadGridColumn } from '@tec/rad-xui/common';
import { ActionItem } from '@tec/rad-ui/common';

        
@Component({
    selector: 'ed-schedule-view',
    styles: [],
    imports:[
        CommonModule, RadPage, RadEjGrid

    ],
    providers: [ScheduleFacade],
    template: `
    <rad-page layout="card" title="Schedule" [loading]="store.loading()" [actions]="actions">
        <rad-ej-grid [dataSource]="store.segments()" [columns]="columns" />
    </rad-page>
    `,
    
})
export class ScheduleView {
        

    protected store = inject(ScheduleFacade);

    columns: RadGridColumn[] = [
        { field: 'name', label: 'Name', type: 'text' },
        { field: 'startDate', label: 'Start Date' },
        { field: 'endDate', label: 'End Date' },
        { field: 'releaseStatus', label: 'Release Status' },
    ];

    actions: ActionItem[] = [
        { label: 'New Segment', icon: 'add', display:'primary', action: this.createSegment.bind(this)  },
       
    ];

    async createSegment(): Promise<void>{
        await this.store.createSegment();
    }

        

        
}
