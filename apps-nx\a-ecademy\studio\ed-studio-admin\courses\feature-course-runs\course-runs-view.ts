import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input } from '@angular/core';
import { RadPage } from '@tec/rad-ui/layout';
import { CourseRunsFacade } from './course-runs-store';
import { RadEjGrid } from '@tec/rad-xui/ej-grid';
import { RadGridColumn } from '@tec/rad-xui/common';
import { ActionItem } from '@tec/rad-ui/common';
import { AppNavigationService } from '@tec/rad-core/abstractions';
import { CourseRunListDto } from '@ed/share/data-academics';

        
@Component({
    selector: 'app-course-runs-view',
    styles: [],
    imports:[
        CommonModule, RadPage, RadEjGrid

    ],
    providers: [CourseRunsFacade],
    template: `
    <rad-page layout="card" title="Course Runs" [loading]="store.loading()" [actions]="actions">
        <rad-ej-grid [dataSource]="store.runs()" [columns]="columns" />
    </rad-page>
    `,
    
})
export class CourseRunsView {


    protected store = inject(CourseRunsFacade);
    #nav = inject(AppNavigationService);

    columns: RadGridColumn[] = [
        { field: 'name', label: 'Name', type: 'text', action: (item)=> this.viewRun(item) },
        { field: 'description', label: 'Description' },
        { field: 'status', label: 'Status' },
    ];

    actions: ActionItem[] = [
        { label: 'New', icon: 'add', display:'primary', action: this.createRun.bind(this)  },
       
    ];

    async createRun(): Promise<void>{
        await this.store.createRun();
    }

    private viewRun(courseRun: CourseRunListDto): void {
        this.#nav.goTo('CourseRun', courseRun.id);
    }




}