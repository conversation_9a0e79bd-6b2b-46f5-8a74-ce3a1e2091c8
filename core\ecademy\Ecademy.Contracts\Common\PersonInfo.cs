﻿
namespace Ecademy.Common.Common;

public class PersonInfo : IPersonInfo
{
    private string _userName;
    public string FirstName { get; set; }
    public string LastName { get; set; }

    public string Username
    {
        get => string.IsNullOrEmpty(_userName) ? EmailAddress : _userName;
        set => _userName = value;
    }

    public string EmailAddress { get; set; }
    public string PhoneNumber { get; set; }

}