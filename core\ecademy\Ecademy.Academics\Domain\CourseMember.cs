﻿namespace Tec.Ecademy.Academics.Domain;

public class CourseMember: DocEntity
{
    public Guid CourseRunId { get; set; }

    public Guid UserId { get; set; }

    public DateTimeOffset EnrolledDate { get; set; }
    
    public DateTimeOffset? CompletionDate { get; set; }

    public Guid StartSegmentId { get; set; }

    public Guid SubscriptionId { get; set; }

    public EnrollmentStatus EnrollmentStatus { get; set; }
}