﻿using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Schedule;

internal class RemoveSegment
{

    internal class Endpoint : CommandEndpoint<Command, ResultObject>
    {

    }

    public class Command : CommandRequest<ResultObject>
    {
        public Guid Id { get; set; }


    }

    

    internal class Handler : DocDbCommandExecutor<Command, ResultObject>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            DocDb.Session.Delete<Segment>(Input.Id);

            


           

        }
    }

}