﻿namespace Ecademy.Common.Core.Storage;

public class DefaultLocalPathResolver : ILocalPathResolver
{

    private string _root ="AppData";


    public DefaultLocalPathResolver()
    {

    }

    public DefaultLocalPathResolver(string root)
    {
        _root = root;
    }
    
    public string MapPath(string path)
    {
        var root = this._root;
        if (string.IsNullOrWhiteSpace(path)) return root;
        path = path.TrimStart('~').TrimStart(new char[] { '/', '\\' });

        var combinedPath = Path.Combine(root, path);

        return combinedPath;
    }
}