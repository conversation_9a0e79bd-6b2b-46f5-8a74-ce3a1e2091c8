﻿namespace Tec.Ecademy.Academics.Features.CourseRuns;

public static class CourseRunQueries
{
    public static async Task<bool> HasOverlappingCourseRuns(this IQuerySession session, Guid courseId, DateOnly startDate, DateOnly endDate)
    {
        // Business rule: Optional constraint to prevent overlapping course runs for same course
        // This might be configurable per organization
        
        var overlappingRuns = await session.Query<CourseRun>()
            .Where(cr => cr.CourseId == courseId &&
                         cr.Status != CourseRunStatus.Cancelled &&
                         cr.Status != CourseRunStatus.Completed &&
                         ((cr.StartDate <= endDate && cr.EndDate >= startDate)))
            .ToListAsync();
            
        return overlappingRuns.Any();
    }
}