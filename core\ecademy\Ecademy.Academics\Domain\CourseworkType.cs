﻿using Ecademy.Common.Core.Media;
using Tec.Rad.Annotations;

namespace Tec.Ecademy.Academics.Domain;

/// <summary>
/// Defines similar properties for course items
/// </summary>
public class CourseworkType : DataEntity<Guid, CourseworkType.Details>, IName
{

    public class Details
    {

    }

    public string Name { get; set; }

    public string Description { get; set; }

    public Guid CourseId { get; set; }

        

    /// <summary>
    /// Indicates item is tracked in course progress reports
    /// </summary>
    public bool IsGraded { get; set; }

    [JsonData]
    public MediaItem Icon { get; set; }

        

    public override string ToString()
    {
            return Name;
        }
}