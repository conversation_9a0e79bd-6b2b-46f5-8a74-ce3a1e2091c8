﻿using Tec.Ecademy.Content.Domain;
using Tec.Rad.Extensions.Data.Migrations;

namespace Tec.Ecademy.Content.Infra.Setup;

public class GradeLevels: Defaults<GradeLevel>
{
    public static GradeLevel Grade1 = Create(1);
    public static GradeLevel Grade2 = Create(2);
    public static GradeLevel Grade3 = Create(3);
    public static GradeLevel Grade4 = Create(4);
    public static GradeLevel Grade5 = Create(5);
    public static GradeLevel Grade6 = Create(6);
    public static GradeLevel Grade7 = Create(7, "Secondary");
    public static GradeLevel Grade8 = Create(8, "Secondary");
    public static GradeLevel Grade9 = Create(9, "Secondary");
    public static GradeLevel Grade10 = Create(10, "Secondary");
    public static GradeLevel Grade11 = Create(11, "Secondary");
    public static GradeLevel Grade12 = Create(12, "Secondary");
    
    public static GradeLevel Create(int levelNumber, string educationLevel = "Primary")
    {
        return new GradeLevel(Guid.NewGuid())
        {
            Name = $"Grade {levelNumber}",
            LevelNumber = levelNumber,
            EducationLevel = educationLevel
        };
    }
}