<Solution>
  <Folder Name="/Ecademy/">
    <Project Path="ecademy\Ecademy.Contracts\Ecademy.Contracts.csproj" />
    <Project Path="ecademy\Ecademy.Core\Ecademy.Core.csproj" />
    <Project Path="ecademy\Ecademy.Sys.ApiDev\Ecademy.Sys.ApiDev.csproj" Type="Classic C#" />
    <Project Path="ecademy\Ecademy.Sys.ApiHost\Ecademy.Sys.ApiHost.csproj" />
    <Project Path="ecademy\Ecademy.Sys.DbMigrator\Ecademy.Sys.DbMigrator.csproj" />
    <Project Path="ecademy\Ecademy.Sys.DocDb\Ecademy.Sys.DocDb.csproj" Type="Classic C#" />
    <Project Path="ecademy\Ecademy.Sys.Infra\Ecademy.Sys.Infra.csproj" />
    <Project Path="ecademy\Ecademy.Sys.Web\Ecademy.Sys.Web.csproj" />
    <File Path="ecademy/common.props" />
    <File Path="ecademy/rootsettings.json" />
  </Folder>
  <Folder Name="/ecademy/Modules/">
    <Project Path="ecademy\Ecademy.Academics.Contracts\Ecademy.Academics.Contracts.csproj" Type="Classic C#" />
    <Project Path="ecademy\Ecademy.Academics.Infra\Ecademy.Academics.Infra.csproj" />
    <Project Path="ecademy\Ecademy.Academics\Ecademy.Academics.csproj" />
    <Project Path="ecademy\Ecademy.Commerce.Infra\Ecademy.Commerce.Infra.csproj" />
    <Project Path="ecademy\Ecademy.Commerce\Ecademy.Commerce.csproj" />
    <Project Path="ecademy\Ecademy.Content.Activities\Ecademy.Content.Activities.csproj" />
    <Project Path="ecademy\Ecademy.Content.Infra\Ecademy.Content.Infra.csproj" />
    <Project Path="ecademy\Ecademy.Content\Ecademy.Content.csproj" />
    <Project Path="ecademy\Ecademy.Platform.Infra\Ecademy.Platform.Infra.csproj" />
    <Project Path="ecademy\Ecademy.Platform\Ecademy.Platform.csproj" />
    <Project Path="ecademy\Ecademy.Tests.Sys\Ecademy.Tests.Sys.csproj" />
  </Folder>
  <Folder Name="/ecademy/Site/">
    <Project Path="ecademy\Ecademy.Website\Ecademy.Website.csproj" />
  </Folder>
  <Folder Name="/ecademy/Tests/" />
  <Folder Name="/Protrac/">
    <Project Path="protrac\Protrac.Contracts\Protrac.Contracts.csproj" />
    <Project Path="protrac\Protrac.Core\Protrac.Core.csproj" />
    <Project Path="protrac\Protrac.Sys.ApiHost\Protrac.Sys.ApiHost.csproj" />
    <Project Path="protrac\Protrac.Sys.DbMigrator\Protrac.Sys.DbMigrator.csproj" />
    <Project Path="protrac\Protrac.Sys.Infra\Protrac.Sys.Infra.csproj" />
    <Project Path="protrac\Protrac.Tests\Protrac.Tests.csproj" />
    <File Path="protrac\common.props" />
    <File Path="protrac\rootsettings.json" />
  </Folder>
  <Folder Name="/Protrac/Modules/">
    <Project Path="protrac\Protrac.Finance\Protrac.Finance.csproj" />
    <Project Path="protrac\Protrac.Org\Protrac.Org.csproj" />
    <Project Path="protrac\Protrac.Work\Protrac.Work.csproj" />
  </Folder>
  <Folder Name="/Solution Items/">
    <File Path="Directory.Builds.props" />
    <File Path="Directory.Packages.props" />
    <File Path="nuget.config" />
  </Folder>
  <Folder Name="/Tec/">
    <Project Path="tec/Tec.Rad.Abp.Web/Tec.Rad.Abp.Web.csproj" />
    <Project Path="tec/Tec.Rad.Abstractions/Tec.Rad.Abstractions.csproj" />
    <Project Path="tec/Tec.Rad.Core.Application/Tec.Rad.Core.Application.csproj" />
    <Project Path="tec/Tec.Rad.Core/Tec.Rad.Core.csproj" />
    <Project Path="tec/Tec.Rad.Extensions.Aspnet/Tec.Rad.Extensions.Aspnet.csproj" />
    <Project Path="tec/Tec.Rad.Extensions.Data/Tec.Rad.Extensions.Data.csproj" />
    <Project Path="tec/Tec.Rad.Extensions.NServiceBus/Tec.Rad.Extensions.NServiceBus.csproj" />
    <Project Path="tec/Tec.Rad.Extensions/Tec.Rad.Extensions.csproj" />
    <Project Path="tec/Tec.Rad.Generators/Tec.Rad.Generators.csproj" />
    <Project Path="tec/Tec.Rad.Sys.Abp/Tec.Rad.Sys.Abp.csproj" />
    <Project Path="tec/Tec.Rad.Sys.Core/Tec.Rad.Sys.Core.csproj" />
    <Project Path="tec/Tec.Rad.Sys.Infra/Tec.Rad.Sys.Infra.csproj" />
    <Project Path="tec/Tec.Rad.Testing/Tec.Rad.Testing.csproj" />
    <Project Path="tec/Tec.Tests.Data/Tec.Tests.Data.csproj" />
    <Project Path="tec/Tec.Tests.Extensions/Tec.Tests.Extensions.csproj" />
    <Project Path="tec/Tec.Tests.IntegrationTests/Tec.Tests.IntegrationTests.csproj" />
    <Project Path="tec/Tec.Tests.TestModule/Tec.Tests.TestModule.csproj" />
    <File Path="tec/common.props" />
    <File Path="tec/rootsettings.json" />
  </Folder>
</Solution>