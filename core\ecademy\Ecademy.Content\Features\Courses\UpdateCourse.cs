using Tec.Rad.Apis;
using Tec.Rad.Cqs;

namespace Tec.Ecademy.Content.Features.Courses;

public static class UpdateCourse
{



    public record Command : CommandRecord<Result>{
        
    }

    public class Result : ResultObject
    {

    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
    }

    public class Endpoint : CommandEndpoint<Command,Result> {

    }

}