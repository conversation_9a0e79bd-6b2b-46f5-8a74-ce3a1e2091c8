<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile kind="Docker">ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj</projectFile>
    <projectFile profileName="Ecademy.ApiHost">ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj</projectFile>
    <projectFile profileName="Ecademy.Sys.ApiHost">ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj</projectFile>
    <projectFile profileName="IIS Express">ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj</projectFile>
    <projectFile>ecademy/Ecademy.Sys.DbMigrator/Ecademy.Sys.DbMigrator.csproj</projectFile>
    <projectFile>ecademy/Ecademy.Sys.DocDb/Ecademy.Sys.DocDb.csproj</projectFile>
    <projectFile profileName="EcademyNSwagGen">ecademy/Ecademy.Sys.NSwagGen/Ecademy.Sys.NSwagGen.csproj</projectFile>
    <projectFile profileName="IIS Express">ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj</projectFile>
    <projectFile profileName="http">ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj</projectFile>
    <projectFile profileName="https">ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj</projectFile>
    <projectFile profileName="Ecademy">ecademy/Ecademy.Website/Ecademy.Website.csproj</projectFile>
    <projectFile profileName="IIS Express">ecademy/Ecademy.Website/Ecademy.Website.csproj</projectFile>
    <projectFile kind="Docker">protrac/Protrac.Sys.ApiHost/Protrac.Sys.ApiHost.csproj</projectFile>
    <projectFile profileName="IIS Express">protrac/Protrac.Sys.ApiHost/Protrac.Sys.ApiHost.csproj</projectFile>
    <projectFile profileName="Protrac.App.ApiHost">protrac/Protrac.Sys.ApiHost/Protrac.Sys.ApiHost.csproj</projectFile>
    <projectFile>protrac/Protrac.Sys.DbMigrator/Protrac.Sys.DbMigrator.csproj</projectFile>
    <projectFile profileName="IIS Express">tec/Tec.Rad.Abp.Web/Tec.Rad.Abp.Web.csproj</projectFile>
    <projectFile profileName="Tec.Rad.Abp.Web">tec/Tec.Rad.Abp.Web/Tec.Rad.Abp.Web.csproj</projectFile>
    <projectFile profileName="Xab.Contracts">tec/Tec.Rad.Abstractions/Tec.Rad.Abstractions.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3f245ca0-8ca2-4b23-89b3-80c75ef1e0d2" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/ecademy/Ecademy.Academics/Domain/EnrollmentStatus.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ecademy/Ecademy.Sys.ApiDev/Ecademy.Sys.ApiDev.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../apps-nx/a-ecademy/ecademy-nswag.js" beforeDir="false" afterPath="$PROJECT_DIR$/../apps-nx/a-ecademy/ecademy-nswag.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../apps-nx/a-ecademy/studio/ed-studio-admin/course-run/feature-run/course-run-page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../apps-nx/a-ecademy/studio/ed-studio-admin/course-run/feature-run/course-run-page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../apps-nx/a-ecademy/studio/ed-studio-admin/course-run/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../apps-nx/a-ecademy/studio/ed-studio-admin/course-run/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../apps-nx/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../apps-nx/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Intelec.Platform.Apps/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Intelec.Platform.Apps/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Intelec.Platform.Apps.slnx" beforeDir="false" afterPath="$PROJECT_DIR$/Intelec.Platform.Apps.slnx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecademy/Ecademy.Academics/Domain/CourseLearner.cs" beforeDir="false" afterPath="$PROJECT_DIR$/ecademy/Ecademy.Academics/Domain/CourseMember.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecademy/Ecademy.Content/Features/Courses/UpdateCourse.cs" beforeDir="false" afterPath="$PROJECT_DIR$/ecademy/Ecademy.Content/Features/Courses/UpdateCourse.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tec/Tec.Rad.Extensions.Aspnet/RadWebModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/tec/Tec.Rad.Extensions.Aspnet/RadWebModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../docs/Ecademy/EcademyArchitecture.md" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/001542c991b14c9cb09ce7ee81d58b958e00/d5/f1239694/IUnitOfWork.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1ff9a1a2d41c43188b54d7b52ee954adbe00/bc/7285100e/IAuditPropertySetter.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1ff9a1a2d41c43188b54d7b52ee954adbe00/da/a8c4cd4b/AuditPropertySetter.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/63557fe3175042daa19aa17d732316c16600/2f/e72d3fc4/IMultiTenant.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/672282e8c4d34f8a8d07c5ae332c7dcd25200/0b/a49ea9f7/Entity`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/672282e8c4d34f8a8d07c5ae332c7dcd25200/5c/d05ba734/Entity.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/672282e8c4d34f8a8d07c5ae332c7dcd25200/74/cfef8759/EntityHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/94c291d90f59476f8f0d215f4b6318152fc00/96/701abfd2/ObjectHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ce9c7a38da294424b3ee12b9bfccacf51a00/0f/db8629fe/IHasCreationTime.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ce9c7a38da294424b3ee12b9bfccacf51a00/b5/05a6b28c/IMayHaveCreator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ce9c7a38da294424b3ee12b9bfccacf51a00/bf/fa7bba4c/ICreationAuditedObject.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1b81cb3be224213a6a73519b6e340a628d9a1fb8629c351a186a26f6376669/List.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1db2b3f44458dcb4cd2087f1d9d2c9c831aa8c17aaae71cf39a14e37e716fa22/IQuerySession.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/1f0fa483babda665af3d8c90c6d160c0c41266b582c88b6fe3824e99ad53b/DocumentSessionBase.Deletes.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2167bf3df4605da63b9ef7c5e4c2a32323eda1f43d225db24dfc67ae5ef55a6/MartenServiceCollectionExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/22bcfb66145058fd188229f0ff78adb546115367255ffda92dd2ff2a755c7c8/MartenRegistry.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2455e8c74f29834df99720dcd3336f9f58ab425461c17d9dee6f38856ee11e/IConnectionLifetime.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/5d20152dd5417b29297754b3f0b2dda895c79623c7561a2ee4eed21081ffe036/IDocumentStore.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/5d52c4fa7045488d6e1b2942621c999744b7b4c5b1a76537c45be3b4a6ddd61/DocumentSessionBase.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/622bb020c88d1593e2f17b7fb61ec39057843844a79fba26bfa94ca4c4c199bc/IDocumentOperations.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/73943e94045f95daafcc048dfce28ec2da38e2f52ed2c8fe12e7ff067597065/LinqExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/79b4b5d07e8c5ac3de172b75667c6bded8e1fa6f42f36d8f6dc2f9e3d568/ArgumentNullException.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/7d16fd87bbe1ba62f3ee4e577f1a47d7a4c7ee4766792d0c9427749b93ec6ed/DocumentMetadataCollection.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/ad14bc9ba26cf5201d3c3df71bbb8b4a74e7e97a103d23d280907cce2c86d6d6/StoreOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bd1d5c50194fea68ff3559c160230b0ab50f5acf4ce3061bffd6d62958e2182/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c7222befae25434c6f78d56fc9a1d63ffcbaef0f0f89afeff65c37181679732/CommandLineHostingExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d7676613a39dfad6dffd8a21a294fc69a4a925cd864f2aca23ee29b25c78/LightweightSession.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d7f9aa4af2ccab678bc855ba93bd5589b5cb387bc4c7b3630fe30e49d5de36b/HostingAbstractionsHostBuilderExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/e4bfbc421d7213d1d168828f58f4983acafbc561947534cb49729a6a8276aec0/IDocumentSession.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Academics/AcademicsModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Academics/AcademicsPermissions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Academics/Domain/CourseRun.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Academics/Features/CourseRuns/CourseRunQueries.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Academics/Features/CourseRuns/CreateCourseRun.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ecademy/Ecademy.Sys.DocDb/Program.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Abp.Web/wwwroot/libs/abp/utils/abp-utils.umd.js.map" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Core.Application/Data/IDocTypeDbProvider.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Core/Apis/RadApiPart.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Core/Domain/DocEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Extensions.Data/Migrations/DefaultsLoader.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tec/Tec.Rad.Extensions.Data/Migrations/DefaultsReader.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="Toolset" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="319Zdo6OpDhvtuC81pA6ZkxuGCv" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.Ecademy.Sys.ApiHost: Ecademy.ApiHost.executor": "Debug",
    ".NET Launch Settings Profile.Ecademy.Sys.NSwagGen: EcademyNSwagGen.executor": "Debug",
    ".NET Launch Settings Profile.Protrac.Sys.ApiHost: IIS Express.executor": "Debug",
    ".NET Launch Settings Profile.Protrac.Sys.ApiHost: Protrac.App.ApiHost.executor": "Debug",
    ".NET Project.Ecademy.Sys.DocDb.executor": "Debug",
    "3cb9d46b-af8f-4282-8b2c-653e533f157a.executor": "Debug",
    "9f89b16d-b21b-4e03-a258-6bc755c58156.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "XThreadsFramesViewSplitterKey": "0.27342418",
    "git-widget-placeholder": "dev",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.augmentcode.intellij.settings",
    "ts.external.directory.path": "D:\\Intelex\\Dev\\Intelec.Platform\\Intelec.Platform.Dev\\apps-nx\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.Ecademy.Sys.NSwagGen: EcademyNSwagGen">
    <configuration name="Ecademy.Sys.DbMigrator" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.DbMigrator/Ecademy.Sys.DbMigrator.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.DocDb" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.DocDb/Ecademy.Sys.DocDb.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Protrac.Sys.DbMigrator" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/protrac/Protrac.Sys.DbMigrator/Protrac.Sys.DbMigrator.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.ApiHost" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Ecademy.Sys.ApiHost" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.ApiHost: Ecademy.ApiHost" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Ecademy.Sys.ApiHost" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.ApiHost: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.ApiHost/Ecademy.Sys.ApiHost.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Ecademy.Sys.ApiHost" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.NSwagGen: EcademyNSwagGen" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.ApiDev/Ecademy.Sys.ApiDev.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="EcademyNSwagGen" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Sys.Web/Ecademy.Sys.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Website: Ecademy" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Website/Ecademy.Website.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Ecademy" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Website: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/ecademy/Ecademy.Website/Ecademy.Website.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Protrac.Sys.ApiHost: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/protrac/Protrac.Sys.ApiHost/Protrac.Sys.ApiHost.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Protrac.Sys.ApiHost: Protrac.App.ApiHost" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/protrac/Protrac.Sys.ApiHost/Protrac.Sys.ApiHost.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Protrac.App.ApiHost" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Tec.Rad.Abp.Web" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/tec/Tec.Rad.Abp.Web/Tec.Rad.Abp.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Tec.Rad.Abp.Web" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Tec.Rad.Abp.Web: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/tec/Tec.Rad.Abp.Web/Tec.Rad.Abp.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Tec.Rad.Abstractions: Xab.Contracts" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/tec/Tec.Rad.Abstractions/Tec.Rad.Abstractions.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value=".NETStandard,Version=v2.1" />
      <option name="LAUNCH_PROFILE_NAME" value="Xab.Contracts" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Ecademy.Sys.ApiHost/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="ecademy.sys.apihost" />
          <option name="contextFolderPath" value="." />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="ecademy/Ecademy.Sys.ApiHost/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="true" />
      <method v="2" />
    </configuration>
    <configuration name="Protrac.Sys.ApiHost/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="protrac.sys.apihost" />
          <option name="contextFolderPath" value="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="protrac/Protrac.Sys.ApiHost/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="true" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.NSwagGen: EcademyNSwagGen" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.ApiHost" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.ApiHost: Ecademy.ApiHost" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.ApiHost: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.Web: http" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.Web: https" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Sys.Web: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Website: Ecademy" />
      <item itemvalue=".NET Launch Settings Profile.Ecademy.Website: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.Protrac.Sys.ApiHost: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.Protrac.Sys.ApiHost: Protrac.App.ApiHost" />
      <item itemvalue=".NET Launch Settings Profile.Tec.Rad.Abp.Web" />
      <item itemvalue=".NET Launch Settings Profile.Tec.Rad.Abp.Web: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.Tec.Rad.Abstractions: Xab.Contracts" />
      <item itemvalue=".NET Project.Ecademy.Sys.DocDb" />
      <item itemvalue=".NET Project.Ecademy.Sys.DbMigrator" />
      <item itemvalue=".NET Project.Protrac.Sys.DbMigrator" />
      <item itemvalue="Docker.Ecademy.Sys.ApiHost/Dockerfile" />
      <item itemvalue="Docker.Protrac.Sys.ApiHost/Dockerfile" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3f245ca0-8ca2-4b23-89b3-80c75ef1e0d2" name="Changes" comment="" />
      <created>1754938339433</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754938339433</updated>
      <workItem from="1754938341713" duration="1215000" />
      <workItem from="1754940214286" duration="4159000" />
      <workItem from="1754944426719" duration="3729000" />
      <workItem from="1754953249404" duration="6889000" />
      <workItem from="1755012629911" duration="9121000" />
      <workItem from="1755041524200" duration="5129000" />
      <workItem from="1755052333977" duration="2360000" />
      <workItem from="1755093925834" duration="13281000" />
      <workItem from="1755114613641" duration="3949000" />
      <workItem from="1755124227874" duration="14760000" />
      <workItem from="1755177339430" duration="11065000" />
      <workItem from="1755203644391" duration="3835000" />
      <workItem from="1755211869360" duration="91000" />
      <workItem from="1755211965919" duration="129000" />
      <workItem from="1755212100376" duration="1065000" />
      <workItem from="1755214878789" duration="23000" />
      <workItem from="1755214951569" duration="9053000" />
      <workItem from="1755273433915" duration="1075000" />
      <workItem from="1755274539274" duration="162000" />
      <workItem from="1755274746557" duration="11704000" />
      <workItem from="1755300923597" duration="7664000" />
      <workItem from="1755343385421" duration="3032000" />
      <workItem from="1755455878310" duration="297000" />
    </task>
    <task id="LOCAL-00001" summary="Create course">
      <option name="closed" value="true" />
      <created>1754953777995</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754953777995</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="Create course" />
    <MESSAGE value="Initial commit" />
    <option name="LAST_COMMIT_MESSAGE" value="" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/tec/Tec.Rad.Extensions.Data/Marten/Features/SqlView.cs</url>
          <line>76</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\tec\Tec.Rad.Extensions.Data\Marten\SqlView.cs" containingFunctionPresentation="Method 'WriteDropStatement'">
            <startOffsets>
              <option value="2400" />
            </startOffsets>
            <endOffsets>
              <option value="2475" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/ecademy/Ecademy.Tests.Sys/Data/LookupTests.cs</url>
          <line>16</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\ecademy\Ecademy.Tests.Content\Data\LookupTests.cs" containingFunctionPresentation="Method 'ShouldGetGradeLevelLookups'">
            <startOffsets>
              <option value="386" />
            </startOffsets>
            <endOffsets>
              <option value="418" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/ecademy/Ecademy.Tests.Sys/Data/LookupTests.cs</url>
          <line>15</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\ecademy\Ecademy.Tests.Content\Data\LookupTests.cs" containingFunctionPresentation="Method 'ShouldGetGradeLevelLookups'">
            <startOffsets>
              <option value="331" />
            </startOffsets>
            <endOffsets>
              <option value="377" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/ecademy/Ecademy.Tests.Sys/Data/SysDataTests.cs</url>
          <line>25</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\ecademy\Ecademy.Tests.Sys\Data\SysDataTests.cs" containingFunctionPresentation="Method 'ShouldGetSubjects'">
            <startOffsets>
              <option value="474" />
            </startOffsets>
            <endOffsets>
              <option value="542" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/ecademy/Ecademy.Tests.Sys/Courses/CoursesTests.cs</url>
          <line>36</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\ecademy\Ecademy.Tests.Sys\Courses\CoursesTests.cs" containingFunctionPresentation="Method 'ShouldCreateCourse'">
            <startOffsets>
              <option value="1111" />
            </startOffsets>
            <endOffsets>
              <option value="1157" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/tec/Tec.Rad.Extensions.Aspnet/RadWebModule.cs</url>
          <line>99</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\tec\Tec.Rad.Extensions.Aspnet\RadWebModule.cs" containingFunctionPresentation="Lambda expression inside Method 'OnApplicationInitialization'">
            <startOffsets>
              <option value="2740" />
            </startOffsets>
            <endOffsets>
              <option value="2856" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/tec/Tec.Rad.Extensions.Aspnet/RadWebModule.cs</url>
          <line>97</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\tec\Tec.Rad.Extensions.Aspnet\RadWebModule.cs" containingFunctionPresentation="Lambda expression inside Method 'OnApplicationInitialization'">
            <startOffsets>
              <option value="2650" />
            </startOffsets>
            <endOffsets>
              <option value="2701" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/tec/Tec.Rad.Extensions/Hosting/WorkerRunner.cs</url>
          <line>46</line>
          <properties documentPath="D:\Intelex\Dev\Intelec.Platform\Intelec.Platform.Dev\core\tec\Tec.Rad.Extensions\Hosting\WorkerRunner.cs" containingFunctionPresentation="Method 'RunAsync'">
            <startOffsets>
              <option value="1469" />
            </startOffsets>
            <endOffsets>
              <option value="1510" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>