{"name": "ed-pep-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/pep/ed-pep-ui/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/pep/ed-pep-ui/ng-package.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/pep/ed-pep-ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "a-ecademy/pep/ed-pep-ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-ecademy/pep/ed-pep-ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}