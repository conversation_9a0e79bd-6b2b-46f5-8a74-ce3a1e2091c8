﻿using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Courseworks;

public static class UpdateCoursework
{
    public class Endpoint : CommandEndpoint<Command, Result>
    {

    }

    public record Command : CommandRecord<Result>
    {
        public Guid Id { get; init; }
        public string Name { get; init; }
        public DateTime? ReleaseDate { get; init; }
        public DateTime? DueDate { get; init; }
        public Guid CourseSubjectId { get; init; }
        public List<Guid> LimitToPlanIds { get; init; } = new List<Guid>();
        public Guid? AssignmentTypeId { get; init; }
        public bool IncludeInTrial { get; init; }
        public string Description { get; init; }
        public int? TimeLimit { get; init; }
        public bool IsTimed { get; init; }
        public int AttemptsAllowed { get; init; } = 1;
        public Guid? SubsectionId { get; init; }
        public int Position { get; init; }
    }

    public class Result : ResultObject
    {
        public Guid Id { get; set; }
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.Id).NotEmpty();
            RuleFor(x => x.Name).NotEmpty().MaximumLength(200);
            RuleFor(x => x.CourseSubjectId).NotEmpty();
            RuleFor(x => x.AttemptsAllowed).GreaterThan(0);
        }
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            var assignment = await DocDb.GetAsync<Domain.Coursework>(Input.Id);
            if (assignment == null)
            {
                throw new BusinessException("Assignment not found");
            }

            // Update assignment properties
            assignment.Name = Input.Name;
            assignment.CourseSubjectId = Input.CourseSubjectId;
            assignment.ReleaseDate = Input.ReleaseDate;
            assignment.DueDate = Input.DueDate;
            assignment.LimitToPlanIds = Input.LimitToPlanIds;
            assignment.AssignmentTypeId = Input.AssignmentTypeId;
            assignment.IncludeInTrial = Input.IncludeInTrial;
            assignment.Description = Input.Description;
            assignment.TimeLimit = Input.TimeLimit;
            assignment.IsTimed = Input.IsTimed;
            assignment.AttemptsAllowed = Input.AttemptsAllowed;
            assignment.SubsectionId = Input.SubsectionId;
            assignment.Position = Input.Position;

            DocDb.Update(assignment);
            Output.Id = assignment.Id;
        }
    }
}