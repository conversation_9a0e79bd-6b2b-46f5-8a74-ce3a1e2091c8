﻿namespace Tec.Ecademy.Academics.Domain;

public enum SegmentBehaviour
{
    Time,
    Chapter
}

public enum SectionContainerType
{
    Section,
    Assignment
}

public class SegmentType : DocEntity, IEntityInfo
{
    public SegmentType(Guid id) : base(id)
    {
    }

    public string Name { get; set; }
    public string Description { get; set; }
    public Guid? ParentId { get; set; }
    public SegmentBehaviour Behaviour { get; set; }
    public SectionContainerType ContainerType { get; set; }
}