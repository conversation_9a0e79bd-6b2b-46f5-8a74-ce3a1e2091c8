﻿<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\common.props" />
  



  <ItemGroup>
    <ProjectReference Include="..\..\tec\Tec.Rad.Core\Tec.Rad.Core.csproj" />
    <ProjectReference Include="..\..\tec\Tec.Rad.Core.Application\Tec.Rad.Core.Application.csproj" />
    <ProjectReference Include="..\Ecademy.Core\Ecademy.Core.csproj" />
    <ProjectReference Include="..\Ecademy.Contracts\Ecademy.Contracts.csproj" />
    <ProjectReference Include="..\Ecademy.Academics.Contracts\Ecademy.Academics.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Tec.Rad.Contracts"></Using>
    <Using Include="Tec.Rad.Cqs"></Using>
    <Using Include="Tec.Rad.Domain"></Using>
    <Using Include="Microsoft.Extensions.Logging"></Using>
    <Using Include="Volo.Abp"></Using>
    <Using Include="FluentValidation"></Using>
    <Using Include="Microsoft.AspNetCore.Authorization"></Using>
  </ItemGroup>



  <ItemGroup>
    <Folder Include="Features\Common\" />
    <Folder Include="ViewModel\" />
    <Folder Include="Views\" />
  </ItemGroup>



  <ItemGroup>
    <Compile Remove="ReadModel\LearningAssignmentInfo.cs" />
  </ItemGroup>



</Project>
