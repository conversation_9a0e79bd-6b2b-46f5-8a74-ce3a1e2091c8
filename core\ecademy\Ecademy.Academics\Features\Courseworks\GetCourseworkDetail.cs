﻿
using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Courseworks;

public class GetCourseworkDetail
{
    public class Endpoint : GetEndpoint<Query, Result>
    {

    }

    public class Query : IQueryRequest<Result>
    {
        public Guid AssignmentId { get; set; }
    }

    public class Result
    {
        public Domain.Coursework Detail { get; set; }
    }

        




    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Detail = await DocDb.GetAsync<Domain.Coursework>(Input.AssignmentId);
        }
    }
}