﻿using Tec.Rad.Domain;
using Tec.Rad.Features.Entities;

namespace Tec.Ecademy.Content.Domain;

[ExpressEntity]
/// <summary>
/// Represent grade levels offered by institution
/// </summary>
public class GradeLevel: DocEntity, IExpressEntity, IName
{
    public GradeLevel(Guid id) : base(id)
    {

    }
    /// <summary>
    /// Name of grade eg. Grade 6
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Used to order grades from highest to lowest
    /// </summary>
    public int LevelNumber { get; set; }

    public string EducationLevel { get; set; }

    public override string ToString()
    {
        return Name;
    }
    
    
}