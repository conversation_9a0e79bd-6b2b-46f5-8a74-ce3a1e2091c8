using System.Runtime.InteropServices;
using Microsoft.Extensions.DependencyInjection;
using Tec.Ecademy.Content.Abstractions;
using Tec.Ecademy.Content.Domain;
using Tec.Ecademy.Content.Features.Courses;
using Tec.Rad.Apis;
using Tec.Rad.Data;
using Tec.Rad.Features.Flex;
using Tec.Rad.Modular;
using Volo.Abp;
using Volo.Abp.Modularity;




namespace Tec.Ecademy.Content;


[DbSchema(ContentModule.DbSchema)]
public class ContentModule: RadModule
{

    public const string DbSchema = "content";
    
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {

        PreConfigure<RadApiOptions>(options =>
        {
            options.Register<ContentModule>("content","Content","content")
                .AddSliceControllers("Features");;
        });
    }

    public override void OnPreApplicationInitialization(ApplicationInitializationContext context)
    {
        RegisterLookups(context.ServiceProvider);
        base.OnPreApplicationInitialization(context);
    }

    private void RegisterLookups(IServiceProvider serviceProvider)
    {
        var lookupProvider = serviceProvider.GetService<ILookupManager>();
        lookupProvider.RegisterDocType<GradeLevel, IContentDb>();
        lookupProvider.RegisterDocType<Course, IContentDb>();
    }
}