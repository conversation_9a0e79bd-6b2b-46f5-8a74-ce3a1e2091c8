select a.id, a.cycle_id, a.node_type, a.name,
a.parent_id, a.release_status, a.start_date, a.end_date, a.position
from 
(
select a.run_section_id id, a.cycle_id,
'Section' node_type, a.name, null parent_id, a.release_status release_status, a.start_date::date, a.end_date::date, 
a.position
from learn.run_section a where a.is_deleted = false
union all
select a.run_subsection_id id, a.cycle_id,
'Subsection' node_type, a.name, a.section_id parent_id, a.release_status release_status, a.start_date::date, a.end_date::date,
a.position
from learn.run_subsection a where a.is_deleted = false
) a