{"name": "ed-pep-guide", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/pep/ed-pep-guide/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/pep/ed-pep-guide/ng-package.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/pep/ed-pep-guide/tsconfig.lib.prod.json"}, "development": {"tsConfig": "a-ecademy/pep/ed-pep-guide/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-ecademy/pep/ed-pep-guide/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}