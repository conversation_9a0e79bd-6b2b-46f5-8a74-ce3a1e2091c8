[33m❯[39m Building...

[1m[33mwarn[39m[22m - The safelist pattern `/^apexcharts-.*$/` doesn't match any Tailwind CSS classes.
[1m[33mwarn[39m[22m - Fix this pattern or remove it from your `safelist` configuration.
[1m[33mwarn[39m[22m - https://tailwindcss.com/docs/content-configuration#safelisting-classes
[32m✔[39m Building...
[37mApplication bundle generation failed. [12.681 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Fluid is not used within the template of LoginPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/auth/views/login/login.page.ts:52:8:[39m[22m
[1m[33m[37m      52 │         [32mFluid[37m[39m[22m
[1m[33m         ╵         [32m~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadShellComponent is not used within the template of RadAppComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/app.component.ts:26:8:[39m[22m
[1m[33m[37m      26 │         [32mRadShellComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: UserMenuComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:41:8:[39m[22m
[1m[33m[37m      41 │         [32mUserMenuComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDrawerComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:43:8:[39m[22m
[1m[33m[37m      43 │         [32mRadDrawerComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadFullscreenComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadFullscreenComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: EmptyLayoutComponent is not used within the template of RadShellComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/shell.component.ts:22:8:[39m[22m
[1m[33m[37m      22 │         [32mEmptyLayoutComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[4[39m[22m[1m[33m3;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of NotFoundView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/views/not-found/not-found.view.ts:15:8:[39m[22m
[1m[33m[37m      15 │         [32mRadHeader[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: ContentRegion is not used within the template of RadViewOutlet[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-core/composition/views/view.outlet.ts:18:8:[39m[22m
[1m[33m[37m      18 │         [32mContentRegion[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of RadFullscreenComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/component/fullscreen/fullscreen.component.ts:14:49:[39m[22m
[1m[33m[37m      14 │ ...atButtonModule, MatTooltipModule, [32mNgTemplateOutlet[37m, MatIconModule][39m[22m
[1m[33m         ╵                                      [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is not used within the template of RadContentLayout[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/content/content.layout.ts:12:26:[39m[22m
[1m[33m[37m      12 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerModule],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of RadDetailPanel[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/detail-panel/detail-panel.ts:33:4:[39m[22m
[1m[33m[37m      33 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is not used within the template of RadViewWrapper[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/view/view.wrapper.ts:37:26:[39m[22m
[1m[33m[37m      37 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerM[39m[22m[1m[33module],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadMenuItemComponent is not used within the template of RadDropdownMenu[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/menu/dropdown/dropdown-menu.component.ts:16:8:[39m[22m
[1m[33m[37m      16 │         [32mRadMenuItemComponent[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of TabNavItemComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav-item.component.ts:21:56:[39m[22m
[1m[33m[37m      21 │ ...tive, MatTooltipModule, [32mNgTemplateOutlet[37m, MatMenuModule, MatIco...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLink is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:76:[39m[22m
[1m[33m[37m      23 │ ...conModule, DragDropModule, [32mRouterLink[37m, RouterLinkActive, TabNav...[39m[22m
[1m[33m         ╵                               [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLinkActive is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:88:[39m[22m
[1m[33m[37m      23 │ ...DragDropModule, RouterLink, [32mRouterLinkActive[37m, TabNavItemComponent][39m[22m
[1m[33m         ╵                                [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: AsyncPipe is not used within the template of RadEjGrid[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/ej-grid/src/data-grid/rad-ej-grid.ts:48:20:[39m[22m
[1m[33m[37m      48 │         GridModule, [32mAsyncPipe[37m, RadGridCell[39m[22m
[1m[33m         ╵                     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected identifier [39m[22m[1m[33mbut found whitespace[0m [1m[35m[plugin angular-compiler][0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/form/form/express.form.scss:2:52:[39m[22m
[1m[33m[37m      2 │ ...field-disabled-input-text-color:[32m[37m var(----mdc-filled-text-field-i...[39m[22m
[1m[33m        ╵                                    [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mUnexpected "var("[0m [1m[35m[plugin angular-compiler][0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/form/form/express.form.scss:2:53:[39m[22m
[1m[33m[37m      2 │ ...ld-disabled-input-text-color: [32mvar([37m----mdc-filled-text-field-inpu...[39m[22m
[1m[33m        ╵                                  [32m~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected identifier but found whitespace[0m [1m[35m[plugin angular-compiler][0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/form/rad-form/form.component.scss:2:52:[39m[22m
[1m[33m[37m      2 │ ...field-disabled-input-text-color:[32m[37m var(----mdc-filled-text-field-i...[39m[22m
[1m[33m        ╵                                    [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mUnexpected "var("[0m [1m[35m[plugin angular-compiler][0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/form/rad-form/form.component.scss:2:53:[39m[22m
[1m[33m[37m      2 │ ...ld-disabled-input-text-color: [32mvar([37m----mdc-filled-text-field-inpu...[39m[22m
[1m[33m        ╵                                  [32m~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseDetailPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-detail/course-detail.page.ts:24:8:[39m[22m
[1m[33m[37m      24 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:40:8:[39m[22m
[1m[33m[37m      40 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43[39m[22m[1m[33m;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDropdownMenu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadDropdownMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Menu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:46:8:[39m[22m
[1m[33m[37m      46 │         [32mMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadPage is not used within the template of CustomerPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/customer.page.ts:20:8:[39m[22m
[1m[33m[37m      20 │         [32mRadPage[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of CustomerDetailView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer-detail.view.ts:60:4:[39m[22m
[1m[33m[37m      60 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadView is not used within the template of CustomerView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer.view.ts:33:8:[39m[22m
[1m[33m[37m      33 │         [32mRadView[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadExpressForm is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:71:8:[39m[22m
[1m[33m[37m      71 │         [32mRadExpressForm[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[39m[22m[1m[33m[43;33m][0m [1mNG8113: RadSpinner is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:75:8:[39m[22m
[1m[33m[37m      75 │         [32mRadSpinner[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:24:8:[39m[22m
[1m[33m[37m      24 │ @import [32m[37m'common/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:25:8:[39m[22m
[1m[33m[37m      25 │ @import [32m[37m'components/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../../a-tec/@rad-ui/theme/evo/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by[39m[22m[1m[33m this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../../a-tec/@rad-app/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:6:8:[39m[22m
[1m[33m[37m      6 │ @import [32m[37m'../../../a-tec/@rad-xui/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:924:15:[39m[22m
[1m[33m[37m      924 │     --spacing-0[32m[37m.5: 0.125rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:925:15:[39m[22m
[1m[33m[37m      925 │     --spacing-1[32m[37m.5: 0.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵      [39m[22m[1m[33m          [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:926:15:[39m[22m
[1m[33m[37m      926 │     --spacing-2[32m[37m.5: 0.625rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:927:15:[39m[22m
[1m[33m[37m      927 │     --spacing-3[32m[37m.5: 0.875rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:952:15:[39m[22m
[1m[33m[37m      952 │     --leading-5[32m[37m.5: 1.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m19 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:38:57:[39m[22m
[1m[31m[37m      38 │ ...ey: 'name', label: 'Name', type: 'input', [32mrow[37m: 1, required: true},[39m[22m
[1m[31m         ╵                                              [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:39:54:[39m[22m
[1m[31m[37m      39 │ ...{ key: 'id', label: 'SKU', type: 'input', [32mrow[37m: 1, required: true},[39m[22m
[1m[31m         ╵                                              [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:40:65:[39m[22m
[1m[31m[37m      40 │ ...abel: 'Type', type: 'select', [32mrow[37m: 2, values: SelectItem.fromVa...[39m[22m
[1m[31m         ╵                                  [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:41:64:[39m[22m
[1m[31m[37m      41 │ ...bel: 'Brand', type: 'select', [32mrow[37m: 2, values: SelectItem.fromVa...[39m[22m
[1m[31m         ╵                                  [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:42:51:[39m[22m
[1m[31m[39m[22m[1m[31m[37m      42 │             { key:'description', type: 'textarea', [32mrow[37m: 3},[39m[22m
[1m[31m         ╵                                                    [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:43:59:[39m[22m
[1m[31m[37m      43 │             { key: 'price', label: 'Price', type: 'input', [32mrow[37m: 4},[39m[22m
[1m[31m         ╵                                                            [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:44:68:[39m[22m
[1m[31m[37m      44 │ ...  { key: 'salePrice', label: 'Sale Price', type: 'input', [32mrow[37m: 4},[39m[22m
[1m[31m         ╵                                                              [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:45:57:[39m[22m
[1m[31m[37m      45 │             { key: 'availableDate',  type: 'datepicker', [32mrow[37m: 5},[39m[22m
[1m[31m         ╵                                                          [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/create-product.ts:46:51:[39m[22m
[1m[31m[37m      46 │             { key: 'endDate',  type: 'datepicker', [32mrow[37m: 5},[39m[22m
[1m[31m         ╵                                                    [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353:[39m[22m[1m[31m Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:116:53:[39m[22m
[1m[31m[37m      116 │ ...y: 'name', label: 'Name', type: 'input', [32mrow[37m: 1, required: true},[39m[22m
[1m[31m          ╵                                             [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:117:50:[39m[22m
[1m[31m[37m      117 │         { key: 'id', label: 'SKU', type: 'input', [32mrow[37m: 1},[39m[22m
[1m[31m          ╵                                                   [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:118:61:[39m[22m
[1m[31m[37m      118 │         { key: 'productType', label: 'Type', type: 'select', [32mrow[37m: 2,[39m[22m
[1m[31m          ╵                                                              [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:125:60:[39m[22m
[1m[31m[37m      125 │ ...bel: 'Brand', type: 'select', [32mrow[37m: 2, values: SelectItem.fromV...[39m[22m
[1m[31m          ╵                                  [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:126:55:[39m[22m
[1m[31m[37m      126 │        [39m[22m[1m[31m { key: 'price', label: 'Price', type: 'input', [32mrow[37m: 4},[39m[22m
[1m[31m          ╵                                                        [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:127:64:[39m[22m
[1m[31m[37m      127 │ ... { key: 'salePrice', label: 'Sale Price', type: 'input', [32mrow[37m: 4},[39m[22m
[1m[31m          ╵                                                             [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:128:53:[39m[22m
[1m[31m[37m      128 │         { key: 'availableDate',  type: 'datepicker', [32mrow[37m: 5},[39m[22m
[1m[31m          ╵                                                      [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:129:47:[39m[22m
[1m[31m[37m      129 │         { key: 'endDate',  type: 'datepicker', [32mrow[37m: 5},[39m[22m
[1m[31m          ╵                                                [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:134:10:[39m[22m
[1m[31m[37m      134 │         { [32mrow[37m:1, key: 'category', label: 'Category', type: 'selec...[39m[22m
[1m[31m          ╵           [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does n[39m[22m[1m[31mot exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:135:10:[39m[22m
[1m[31m[37m      135 │         { [32mrow[37m:2,  key:'description', type: 'textarea', required: ...[39m[22m
[1m[31m          ╵           [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:158:53:[39m[22m
[1m[31m[37m      158 │ ...y: 'name', label: 'Name', type: 'input', [32mrow[37m: 1, required: true},[39m[22m
[1m[31m          ╵                                             [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[37mWatch mode enabled. Watching for file changes...[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37mApplication bundle generation failed. [4.487 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:24:8:[39m[22m
[1m[33m[37m      24 │ @import [32m[37m'common/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:25:8:[39m[22m
[1m[33m[37m      25 │ @import [32m[37m'components/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../../a-tec/@rad-ui/theme/evo/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][[39m[22m[1m[33m0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../../a-tec/@rad-app/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:6:8:[39m[22m
[1m[33m[37m      6 │ @import [32m[37m'../../../a-tec/@rad-xui/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:924:15:[39m[22m
[1m[33m[37m      924 │     --spacing-0[32m[37m.5: 0.125rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:925:15:[39m[22m
[1m[33m[37m      925 │     --spacing-1[32m[37m.5: 0.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:926:15:[39m[22m
[1m[33m[37m      926 │     --spacing-2[32m[37m.5: 0.625rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0[39m[22m[1m[33mm[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:927:15:[39m[22m
[1m[33m[37m      927 │     --spacing-3[32m[37m.5: 0.875rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:952:15:[39m[22m
[1m[33m[37m      952 │     --leading-5[32m[37m.5: 1.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m19 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Fluid is not used within the template of LoginPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/auth/views/login/login.page.ts:52:8:[39m[22m
[1m[33m[37m      52 │         [32mFluid[37m[39m[22m
[1m[33m         ╵         [32m~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadShellComponent is not used within the template of RadAppComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/app.component.ts:26:8:[39m[22m
[1m[33m[37m      26 │         [32mRadShellComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: UserMenuComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:41:8:[39m[22m
[1m[33m[37m      41 │         [32mUserMenuComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDrawerComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin ang[39m[22m[1m[33mular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:43:8:[39m[22m
[1m[33m[37m      43 │         [32mRadDrawerComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadFullscreenComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadFullscreenComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: EmptyLayoutComponent is not used within the template of RadShellComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/shell.component.ts:22:8:[39m[22m
[1m[33m[37m      22 │         [32mEmptyLayoutComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of NotFoundView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/views/not-found/not-found.view.ts:15:8:[39m[22m
[1m[33m[37m      15 │         [32mRadHeader[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: ContentRegion is not used within the template of RadViewOutlet[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-core/composition/views/view.outlet.ts:18:8:[39m[22m
[1m[33m[37m      18 │         [32mContentRegion[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of RadFullscreenComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/component/fullscreen/fullscreen.component.ts:14:49:[39m[22m
[1m[33m[37m      14 │ ...atButtonModule, MatTooltipModule, [32mNgTemplateOutlet[37m, MatIconModule][39m[22m
[1m[33m         ╵                                      [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is no[39m[22m[1m[33mt used within the template of RadContentLayout[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/content/content.layout.ts:12:26:[39m[22m
[1m[33m[37m      12 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerModule],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of RadDetailPanel[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/detail-panel/detail-panel.ts:33:4:[39m[22m
[1m[33m[37m      33 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is not used within the template of RadViewWrapper[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/view/view.wrapper.ts:37:26:[39m[22m
[1m[33m[37m      37 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerModule],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadMenuItemComponent is not used within the template of RadDropdownMenu[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/menu/dropdown/dropdown-menu.component.ts:16:8:[39m[22m
[1m[33m[37m      16 │         [32mRadMenuItemComponent[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of TabNavItemComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav-item.component.ts:21:56:[39m[22m
[1m[33m[37m      21 │ ...tive, MatTooltipModule, [32mNgTemplateOutlet[37m, MatMenuModule, MatIco...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLink is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:76:[39m[22m
[1m[33m[37m      23 │ ...conModule, DragDropModule, [32mRouterLink[[39m[22m[1m[33m37m, RouterLinkActive, TabNav...[39m[22m
[1m[33m         ╵                               [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLinkActive is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:88:[39m[22m
[1m[33m[37m      23 │ ...DragDropModule, RouterLink, [32mRouterLinkActive[37m, TabNavItemComponent][39m[22m
[1m[33m         ╵                                [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: AsyncPipe is not used within the template of RadEjGrid[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/ej-grid/src/data-grid/rad-ej-grid.ts:48:20:[39m[22m
[1m[33m[37m      48 │         GridModule, [32mAsyncPipe[37m, RadGridCell[39m[22m
[1m[33m         ╵                     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseDetailPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-detail/course-detail.page.ts:24:8:[39m[22m
[1m[33m[37m      24 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:40:8:[39m[22m
[1m[33m[37m      40 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDropdownMenu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadDropdownMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Menu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@templ[39m[22m[1m[33mate-app/src/modules/academy/course-list/course-list.page.ts:46:8:[39m[22m
[1m[33m[37m      46 │         [32mMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadPage is not used within the template of CustomerPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/customer.page.ts:20:8:[39m[22m
[1m[33m[37m      20 │         [32mRadPage[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of CustomerDetailView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer-detail.view.ts:60:4:[39m[22m
[1m[33m[37m      60 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadView is not used within the template of CustomerView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer.view.ts:33:8:[39m[22m
[1m[33m[37m      33 │         [32mRadView[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadExpressForm is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:71:8:[39m[22m
[1m[33m[37m      71 │         [32mRadExpressForm[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadSpinner is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:75:8:[39m[22m
[1m[33m[37m      75 │         [32mRadSpinner[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:134:10:[39m[22m
[1m[31m[37m      134 │         { [32mrow[37m:1, key: 'category', label: 'Category', type: 'selec...[39m[22m
[1m[31m          ╵           [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2353: Object literal may only specify known properties, and 'row' does not exist in type 'RadFormItem'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:135:10:[39m[22m
[1m[31m[37m      135 │         { [32mrow[37m:2,  key:'description', type: 'textarea', required: ...[39m[22m
[1m[31m          ╵           [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m               [2m | [22m  [36m2.87 MB[39m[37m[2m | [22m[39m
[37m[32mchunk-EVSPGG2W.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m  [36m1.64 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-BOMGPLG2.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m[36m984 bytes[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                 [2m | [22m[36m407 bytes[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m            [2m | [22m [36m95 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m        [2m | [22m  [1m2.87 MB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-HVGDDNN3.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m[36m301.95 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ZDMYNQ6H.js[39m[37m  [2m | [22m[2mbootstrap[22m            [2m | [22m[36m221.16 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XOJF2G7H.js[39m[37m  [2m | [22m[2mproduct-detail[22m       [2m | [22m[36m152.03 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-KQA7INAO.js[39m[37m  [2m | [22m[2mindex[22m                [2m | [22m [36m76.55 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RHPY4SVW.js[39m[37m  [2m | [22m[2mdense-component[22m      [2m | [22m [36m67.67 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HFUDG2H5.js[39m[37m  [2m | [22m[2mnot-found-view[22m       [2m | [22m [36m66.03 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PDZBROUZ.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m58.22 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-VHXOM77P.js[39m[37m  [2m | [22m[2mschool-dashboard-page[22m[2m | [22m [36m49.71 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-BQY3TF3F.js[39m[37m  [2m | [22m[2mindex[22m                [2m | [22m [36m46.19 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7JQLPHPM.js[39m[37m  [2m | [22m[2mcourse-list-page[22m     [2m | [22m [39m[37m[36m32.97 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XLO5QEFU.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m24.04 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RQOQGD2K.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m23.61 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-AU5723I7.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m21.51 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-O3TAA43T.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m14.76 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-K4G2S34I.js[39m[37m  [2m | [22m[2m-[22m                    [2m | [22m [36m14.57 kB[39m[37m[2m | [22m[39m
[37m[2m...and 24 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [1.710 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:24:8:[39m[22m
[1m[33m[37m      24 │ @import [32m[37m'common/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/theme/evo/styles.scss:25:8:[39m[22m
[1m[33m[37m      25 │ @import [32m[37m'components/index.scss';[39m[22m
[1m[33m         ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../../a-tec/@rad-ui/theme/evo/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][[39m[22m[1m[33m0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../../a-tec/@rad-app/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:6:8:[39m[22m
[1m[33m[37m      6 │ @import [32m[37m'../../../a-tec/@rad-xui/styles/styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The plugin "angular-sass" was triggered by this import[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:924:15:[39m[22m
[1m[33m[37m      924 │     --spacing-0[32m[37m.5: 0.125rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:925:15:[39m[22m
[1m[33m[37m      925 │     --spacing-1[32m[37m.5: 0.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:926:15:[39m[22m
[1m[33m[37m      926 │     --spacing-2[32m[37m.5: 0.625rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0[39m[22m[1m[33mm[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:927:15:[39m[22m
[1m[33m[37m      927 │     --spacing-3[32m[37m.5: 0.875rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mExpected ":"[0m [css-syntax-error][39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/styles.scss:952:15:[39m[22m
[1m[33m[37m      952 │     --leading-5[32m[37m.5: 1.375rem;[39m[22m
[1m[33m          │                [32m^[37m[39m[22m
[1m[33m          ╵                [32m:[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m19 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    angular:styles/global:styles:1:8:[39m[22m
[1m[33m[37m      1 │ @import [32m'a-tec/@template-app/src/styles.scss'[37m;[39m[22m
[1m[33m        ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Fluid is not used within the template of LoginPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/auth/views/login/login.page.ts:52:8:[39m[22m
[1m[33m[37m      52 │         [32mFluid[37m[39m[22m
[1m[33m         ╵         [32m~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadShellComponent is not used within the template of RadAppComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/app.component.ts:26:8:[39m[22m
[1m[33m[37m      26 │         [32mRadShellComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: UserMenuComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:41:8:[39m[22m
[1m[33m[37m      41 │         [32mUserMenuComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDrawerComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin ang[39m[22m[1m[33mular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:43:8:[39m[22m
[1m[33m[37m      43 │         [32mRadDrawerComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadFullscreenComponent is not used within the template of DenseLayoutComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/dense/dense.component.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadFullscreenComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: EmptyLayoutComponent is not used within the template of RadShellComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/layouts/shell.component.ts:22:8:[39m[22m
[1m[33m[37m      22 │         [32mEmptyLayoutComponent[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of NotFoundView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-app/shell/views/not-found/not-found.view.ts:15:8:[39m[22m
[1m[33m[37m      15 │         [32mRadHeader[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: ContentRegion is not used within the template of RadViewOutlet[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-core/composition/views/view.outlet.ts:18:8:[39m[22m
[1m[33m[37m      18 │         [32mContentRegion[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of RadFullscreenComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/component/fullscreen/fullscreen.component.ts:14:49:[39m[22m
[1m[33m[37m      14 │ ...atButtonModule, MatTooltipModule, [32mNgTemplateOutlet[37m, MatIconModule][39m[22m
[1m[33m         ╵                                      [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is no[39m[22m[1m[33mt used within the template of RadContentLayout[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/content/content.layout.ts:12:26:[39m[22m
[1m[33m[37m      12 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerModule],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of RadDetailPanel[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/detail-panel/detail-panel.ts:33:4:[39m[22m
[1m[33m[37m      33 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadScrollView is not used within the template of RadViewWrapper[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/layout/view/view.wrapper.ts:37:26:[39m[22m
[1m[33m[37m      37 │   imports: [CommonModule, [32mRadScrollView[37m, ProgressSpinnerModule],[39m[22m
[1m[33m         ╵                           [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadMenuItemComponent is not used within the template of RadDropdownMenu[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/menu/dropdown/dropdown-menu.component.ts:16:8:[39m[22m
[1m[33m[37m      16 │         [32mRadMenuItemComponent[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: NgTemplateOutlet is not used within the template of TabNavItemComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav-item.component.ts:21:56:[39m[22m
[1m[33m[37m      21 │ ...tive, MatTooltipModule, [32mNgTemplateOutlet[37m, MatMenuModule, MatIco...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLink is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:76:[39m[22m
[1m[33m[37m      23 │ ...conModule, DragDropModule, [32mRouterLink[[39m[22m[1m[33m37m, RouterLinkActive, TabNav...[39m[22m
[1m[33m         ╵                               [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RouterLinkActive is not used within the template of RadTabNavComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-ui/tab/tab-nav/tab-nav.component.ts:23:88:[39m[22m
[1m[33m[37m      23 │ ...DragDropModule, RouterLink, [32mRouterLinkActive[37m, TabNavItemComponent][39m[22m
[1m[33m         ╵                                [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: AsyncPipe is not used within the template of RadEjGrid[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@rad-xui/ej-grid/src/data-grid/rad-ej-grid.ts:48:20:[39m[22m
[1m[33m[37m      48 │         GridModule, [32mAsyncPipe[37m, RadGridCell[39m[22m
[1m[33m         ╵                     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseDetailPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-detail/course-detail.page.ts:24:8:[39m[22m
[1m[33m[37m      24 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadCard is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:40:8:[39m[22m
[1m[33m[37m      40 │         [32mRadCard[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadDropdownMenu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/academy/course-list/course-list.page.ts:44:8:[39m[22m
[1m[33m[37m      44 │         [32mRadDropdownMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: Menu is not used within the template of CourseListPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@templ[39m[22m[1m[33mate-app/src/modules/academy/course-list/course-list.page.ts:46:8:[39m[22m
[1m[33m[37m      46 │         [32mMenu[37m,[39m[22m
[1m[33m         ╵         [32m~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadPage is not used within the template of CustomerPage[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/customer.page.ts:20:8:[39m[22m
[1m[33m[37m      20 │         [32mRadPage[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadHeader is not used within the template of CustomerDetailView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer-detail.view.ts:60:4:[39m[22m
[1m[33m[37m      60 │     [32mRadHeader[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadView is not used within the template of CustomerView[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/customers/feature-customer-view/customer.view.ts:33:8:[39m[22m
[1m[33m[37m      33 │         [32mRadView[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadExpressForm is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:71:8:[39m[22m
[1m[33m[37m      71 │         [32mRadExpressForm[37m,[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8113: RadSpinner is not used within the template of ProductDetail[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    a-tec/@template-app/src/modules/crm/products/product-detail.ts:75:8:[39m[22m
[1m[33m[37m      75 │         [32mRadSpinner[37m[39m[22m
[1m[33m         ╵         [32m~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mNOTE: Raw file sizes do not reflect development server per-request transformations.[39m
  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4265[22m/[39m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
[2m11:49:25 AM[22m [33m[1m[vite][22m[39m [33m[2m(client)[22m[39m [33mwarning: 
[36mD:/Intelex/Dev/Intelec.Platform/Intelec.Platform.Dev/apps-nx/.angular/cache/20.0.6/template-app/vite/deps/@angular-architects_module-federation.js[33m
[0m66 |      return yield import(
67 |        /* webpackIgnore:true */
68 |        remoteEntry
   |        ^^^^^^^^^^^
69 |      ).then((container) => {
70 |        initRemote(container, remoteEntry);[0m[33m
The above dynamic import cannot be analyzed by Vite.
See [34mhttps://github.com/rollup/plugins/tree/master/packages/dynamic-import-vars#limitations[33m for supported dynamic import formats. If this is intended to be left as-is, you can use the /* @vite-ignore */ comment inside the import() call to suppress this warning.
[33m[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mD:/Intelex/Dev/Intelec.Platform/Intelec.Platform.Dev/apps-nx/.angular/cache/20.0.6/template-app/vite/deps/@angular-architects_module-federation.js?v=d22fdfc8[39m
