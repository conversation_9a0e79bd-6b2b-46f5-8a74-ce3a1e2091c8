namespace Tec.Ecademy.Sys.ApiDev;

public class Program
{
    public static async Task<int> Main(string[] args)
    {
        try
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add ABP with minimal module
            builder.Services.AddApplication<EcademyNSwagModule>();

            var app = builder.Build();

            // Initialize ABP application
            await app.InitializeApplicationAsync();

            // Configure for development only
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            await app.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
            return 1;
        }
    }
}