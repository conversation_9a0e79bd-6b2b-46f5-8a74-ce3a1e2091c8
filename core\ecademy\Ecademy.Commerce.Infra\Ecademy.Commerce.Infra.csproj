﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\common.props"></Import>

	

  <ItemGroup>
    <ProjectReference Include="..\..\tec\Tec.Rad.Extensions\Tec.Rad.Extensions.csproj" />
    <ProjectReference Include="..\..\tec\Tec.Rad.Extensions.Data\Tec.Rad.Extensions.Data.csproj" />
    <ProjectReference Include="..\Ecademy.Commerce\Ecademy.Commerce.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="HtmlAgilityPack" />
    <PackageReference Include="NJsonSchema" />
    <PackageReference Include="RestSharp" />
  </ItemGroup>

</Project>
