import { ModuleInfo } from "@tec/rad-core/abstractions";
import { radCompositeRoute } from '@tec/rad-core/composition';


export const AdminCourseRunModule: ModuleInfo = {
    name: 'AdminCourseRunModule',
    path: "course-run",
    routes: [
        {
            name: 'CourseRun', path: ':courseRunId', id:'courseRunId',
            loadComponent: () => import('./feature-run').then(m => m.CourseRunPage),
            children: [
                ...radCompositeRoute("CourseRunDetail"),
            ]
        }
        
    ],
    views: [
        {name: 'CourseRunDetail', loadComponent: () => import('./feature-run').then((m) => m.RunDetailView)},
        {name: 'CourseRunSchedule', loadComponent: () => import('./feature-schedule').then((m) => m.ScheduleView)},
    ]

}