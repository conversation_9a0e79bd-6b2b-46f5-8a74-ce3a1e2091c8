﻿using Tec.Rad.Apis;
using Tec.Rad.Cqs;
using Volo.Abp.Application.Dtos;

namespace Tec.Ecademy.Content.Features.Courses;

public static class GetCourses
{



    public record Query : IQueryRequest<Result>;

    public class CourseDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public Guid GradeId { get; set; }
        public string GradeName { get; set; }
        
    }

    private const string SqlQuery = @"
select c.id, c.name, c.description, c.grade_id, g.name grade_name
from content.xr_course_dv c
inner join content.xr_grade_level_dv g on c.grade_id = g.id
";
    
    public class Result: PagedResultDto<CourseDto>
    {
    
    }

    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Items = await Db.SelectListAsync<CourseDto>(SqlQuery);
        }
    }

   
    public class Endpoint : GetEndpoint<Query, Result>
    {

    }



}