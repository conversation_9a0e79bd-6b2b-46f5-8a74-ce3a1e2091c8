﻿using Tec.Rad.Apis;
using Tec.Rad.Modular;
using Volo.Abp.Modularity;

namespace Tec.Ecademy.Platform;

public class PlatformModule: RadModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<RadApiOptions>(options =>
        {

            options.Register<PlatformModule>(o =>
            {
                o.RootPath = "platform";
                o.ApiGroup = "platform";
                o.DefaultEndpointName = "Platform";
               


            });

        });

    }
}