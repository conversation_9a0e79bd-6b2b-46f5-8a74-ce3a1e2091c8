﻿using Tec.Ecademy.Academics.Features.Schedule.Shared;
using Tec.Rad.Apis;
using Tec.Rad.SqlKata;

namespace Tec.Ecademy.Academics.Features.Schedule;

public class GetRunSegments
{
    public class Query : IQueryRequest<Result>
    {
        public Guid CycleId { get; set; }
    }

    public class Result
    {
        public List<RunSegmentNode> Items { get; set; }
    }

    internal class Executor : DocDbQueryExecutor<Query, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Items = await this.Db.ExpressionQuery<RunSegmentNode>()
                .ToListAsync(n => n.CycleId, Input.CycleId);
        }
    }

    public class Endpoint : GetEndpoint<Query, Result>
    {
    }
}