namespace Ecademy.Common.Core.Authorization;

public class SysPermissions : IPermissions
{
    public class Lesson
    {
        public const string Preview = "Lesson.Preview";
        public const string Create = "Lesson.Create";
    }

    public class Application
    {
        public const string Studio = "Application.Studio";
        public const string Workbook = "Application.Workbook";
    }

    public class Registry
    {
        public const string Staff = "Registry.Staff";
    }


    public class Activity
    {
        public const string Create = "Activity.Create";
        public const string Edit = "Actvitiy.Edit";
        public const string Publish = "Activity.Publish";
        public const string Review = "Activity.Review";
        public const string Take = "Activity.Take";
            
    }

    public class Subscription
    {
        public const string Create = "Subscription.Create";
        public const string Change = "Subscription.Change";

    }


    public class StudentActivity
    {
            
        public const string Review = "StudentActivity.Review";
        public const string Take = "StudentActivity.Take";
        public const string Mark = "StudentActivity.Mark";
    }


    public class Course
    {

        public const string Students = "Course.Students";
        public const string Activities = "Course.Activities";
        public const string Assignments = "Course.Assignments";
        public const string Subjects = "Course.Subjects";
    }

    public class ParentPortal
    {
        public const string Access = "ParentPortal.Access";
    }


    public class Accounts
    {
        public const string ResetPassword = "Accounts.ResetPassword";
    }
        




}