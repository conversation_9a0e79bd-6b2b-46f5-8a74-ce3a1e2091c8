﻿using Tec.Ecademy.Academics.Abstractions;
using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Schedule;

public static class MoveSegment
{
    public class Endpoint : CommandEndpoint<Input, Result>
    {
    }

    public class Input : CommandRequest<Result>
    {
        public Guid CourseRunId { get; set; }
        public List<ItemOrder> Items { get; set; }
    }

    public class Result : ResultObject
    {
    }

    internal class Handler : DocDbCommandExecutor<Input, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            var sections = await DocDb.Query<Segment>().Where(n => n.CourseRunId == Input.CourseRunId).ToListAsync();
            var lookup = sections.ToDictionary(n => n.Id);
            foreach (var index in Input.Items)
            {
                var section = lookup.GetValueOrDefault(index.ItemId);
                if (section != null)
                {
                    section.Order = index.Order;
                    DocDb.Update(section);
                }
            }
        }
    }
}