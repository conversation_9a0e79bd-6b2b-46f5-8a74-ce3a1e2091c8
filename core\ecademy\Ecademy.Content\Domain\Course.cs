﻿using Tec.Rad.Domain;

namespace Tec.Ecademy.Content.Domain;

public class Course: DocEntity, IName
{
    public string Name { get; set; }

    public string? Description { get; set; }

    public Guid? GradeId { get; set; }

    // Enhanced properties for MVP
    public List<Guid> SubjectIds { get; set; } = new();
    public string? ImageUrl { get; set; }
    public CourseStatus Status { get; set; } = CourseStatus.Draft;
    public Guid CreatedByInstructorId { get; set; }
    public DateTime? PublishedDate { get; set; }
    public string? Prerequisites { get; set; }
    public int EstimatedDurationHours { get; set; }
    public string? LearningObjectives { get; set; }
}

public enum CourseStatus
{
    Draft,
    Published,
    Archived
}