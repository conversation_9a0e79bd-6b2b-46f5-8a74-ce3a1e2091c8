﻿using Tec.Rad.Apis;
using Tec.Rad.Utils;

namespace Tec.Ecademy.Academics.Features.CourseRuns;

public static class CreateCourseRun
{



    public record Command : ICommandRequest<Result>
    {
        public required string Name { get; init; }
        public string? Description { get; init; }
        public required Guid CourseId { get; init; }
        public required DateOnly StartDate { get; init; }
        public required DateOnly EndDate { get; init; }
        public Guid? YearId { get; init; }
    }

    public class Result : ResultObject
    {
        public Guid CourseRunId { get; set; }
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Course run name is required")
                .MaximumLength(200)
                .WithMessage("Course run name cannot exceed 200 characters");
                
            RuleFor(x => x.Description)
                .MaximumLength(2000)
                .WithMessage("Description cannot exceed 2000 characters");
                
            RuleFor(x => x.CourseId)
                .NotEmpty()
                .WithMessage("Course ID is required");
                
            RuleFor(x => x.StartDate)
                .Must(BeInFuture)
                .WithMessage("Start date must be in the future");
                
            RuleFor(x => x.EndDate)
                .Must((cmd, endDate) => endDate > cmd.StartDate)
                .WithMessage("End date must be after start date");
                
            
                
           
        }
        
        private bool BeInFuture(DateOnly date)
        {
            return date > DateTimeOffset.UtcNow.AddHours(-1).Date.DateOnly(); // Allow 1 hour buffer for timezone issues
        }
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected async override Task Execute(CancellationToken cancellationToken)
        {
            Logger.LogInformation("Creating course run '{Name}' for course {CourseId} by user {UserId}", 
                Input.Name, Input.CourseId, CurrentUser.Id);
            
            if(await DocDb.Session.HasOverlappingCourseRuns(Input.CourseId, Input.StartDate, Input.EndDate))
            {
                Output.Conflict("OverlappingCourseRuns", "Course run overlaps with existing course run");
            }
            
            // Create course run entity
            var courseRun = new CourseRun(NewId())
            {
                Name = Input.Name.Trim(),
                Description = Input.Description?.Trim() ?? string.Empty,
                CourseId = Input.CourseId,
                Status = CourseRunStatus.Draft,
                StartDate = Input.StartDate,
                EndDate = Input.EndDate,
                CurrentEnrollments = 0,
                YearId = Input.YearId,
            };
            
            DocDb.Insert(courseRun);
            Output.CourseRunId = courseRun.Id;
            
            
        }
        
        
            
            
        
    }

    
    public class Endpoint : CommandEndpoint<Command,Result> {

    }

}