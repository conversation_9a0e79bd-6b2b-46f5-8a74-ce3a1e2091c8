﻿--Sql for CourseworkInfo
--TODO: Set file build action to EmbeddedResource and replace with Sql for view
select a.coursework_id, a.coursework_id id, a.tenant_id, a.cycle_id, a.coursework_number, a.name, a.release_status, a.activity_id, a.activity_type,
       a.course_subject_id, s.subject_id, s.name subject, a.coursework_type_id, t.name coursework_type,
       sc.run_section_id section_id, sc.name section, a.subsection_id, sb.name subsection, a.position


from learn.coursework a
left join learn.course_subject s on a.course_subject_id = s.course_subject_id
left join learn.coursework_type t on a.coursework_type_id = t.coursework_type_id
left join learn.run_subsection sb on a.subsection_id = sb.run_subsection_id
left join learn.run_section sc on sb.section_id = sc.run_section_id
where a.is_deleted = false
