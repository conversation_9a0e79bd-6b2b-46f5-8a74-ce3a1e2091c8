{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "dist": false, "node_modules": false, ".angular": true, ".fttemplates": false, ".idea": true, ".nx": true, "tmp": true, "e2e": true, "libs/**/README.md": true, "*/**/{tsconfig*.json,README.md,jest.config.ts}": true, "*/**/{package.json,ng-package.json}": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/.nx/**": true}, "favorites.sortDirection": "ASC", "explorer.fileNesting.enabled": false, "explorer.fileNesting.patterns": {"*.ts": "$(capture).html, $(capture).scss, $(capture).module.ts, $(capture).spec.ts"}, "explorer.fileNesting.expand": false, "eslint.validate": ["json"], "angular.enable-strict-mode-prompt": false, "nxConsole.generateAiAgentRules": true, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.suggest.paths": true, "typescript.preferences.importModuleSpecifier": "shortest", "typescript.preferences.importModuleSpecifierEnding": "minimal", "debug.javascript.autoAttachFilter": "smart", "debug.javascript.terminalOptions": {"skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, "debug.javascript.autoAttachSmartPattern": ["${workspaceFolder}/**", "!**/node_modules/**", "**/$KNOWN_TOOLS$/**"]}