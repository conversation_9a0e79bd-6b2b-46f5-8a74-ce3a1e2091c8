﻿using Tec.Ecademy.Academics.Abstractions;
using Tec.Rad.Apis;

namespace Tec.Ecademy.Academics.Features.Courseworks;

/// <summary>
/// Order coursework in a section
/// </summary>
public static class MoveCoursework
{
    public class Endpoint : CommandEndpoint<Command, Result> { }

    public class Command : CommandRequest<Result>
    {
        public Guid SegmentId { get; set; }
        public List<ItemOrder> Items { get; set; }
    }

    public class Result : ResultObject
    {
    }

    internal class Handler : DocDbCommandExecutor<Command, Result>
    {
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            var assignments = await DocDb.Query<Domain.Coursework>()
                .Where(n => n.SubsectionId == Input.SegmentId)
                .ToListAsync();

            var lookup = assignments.ToDictionary(n => n.Id);

            foreach (var index in Input.Items)
            {
                var assignment = lookup.GetValueOrDefault(index.ItemId);
                if (assignment != null && assignment.Position != index.Order)
                {
                    assignment.Position = index.Order;
                    DocDb.Update(assignment);
                }
            }
        }
    }
}