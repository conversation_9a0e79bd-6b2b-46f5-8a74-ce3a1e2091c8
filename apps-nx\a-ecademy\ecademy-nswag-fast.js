const generateProxy = require('../service.proxy')
const { spawn } = require('child_process');
const path = require('path');

// Configuration for fast NSwag generation using lightweight server
const config = {
  url: "localhost:5555",  // Lightweight server port
  proxies: [
    { api:"content", output: "a-ecademy/shared/ed-shared/data-content/proxy/api-client.ts"},
    { api:"academics", output: "a-ecademy/shared/ed-shared/data-academics/proxy/api-client.ts"}
  ]
}

async function generateWithLightweightServer() {
  console.log('🚀 Starting fast NSwag generation...');

  // Path to the lightweight server
  const serverPath = path.join(__dirname, '../../core/ecademy/Ecademy.Sys.NSwagGen');

  console.log('📦 Starting lightweight API server...');

  // Start the lightweight server
  const serverProcess = spawn('dotnet', ['run'], {
    cwd: serverPath,
    stdio: 'pipe'
  });

  // Wait for server to start
  await new Promise((resolve) => {
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`Server: ${output.trim()}`);

      // Look for indication that server is ready
      if (output.includes('Now listening on') || output.includes('Application started')) {
        console.log('✅ Server is ready!');
        setTimeout(resolve, 2000); // Give it a moment to fully initialize
      }
    });

    serverProcess.stderr.on('data', (data) => {
      console.log(`Server Error: ${data.toString().trim()}`);
    });
  });

  try {
    console.log('🔄 Generating API proxies...');

    // Generate the proxies
    await new Promise((resolve, reject) => {
      generateProxy(config, "Ecademy", (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ API proxies generated successfully!');

  } catch (error) {
    console.error('❌ Error generating proxies:', error);
  } finally {
    console.log('🛑 Stopping server...');
    serverProcess.kill();
    console.log('✅ Done!');
  }
}

// Run the generation
generateWithLightweightServer().catch(console.error);