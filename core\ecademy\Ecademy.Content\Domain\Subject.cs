﻿using Tec.Rad.Domain;

namespace Tec.Ecademy.Content.Domain;

/// <summary>
/// An academic subject
/// </summary>
public class Subject : DocEntity, IEntityInfo, IName
{


    public Subject(Guid id) : base(id)
    {
    }
    

    public string Name { get; set; }



    public override string ToString()
    {
        return Name;
    }
    
    
    
    public static Subject Create(string name)
    {
        return new Subject(Guid.NewGuid())
        {
            Name = name
        };
    }
}