﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
	  <RootNamespace>Ecademy.Registry.Infra</RootNamespace>
	</PropertyGroup>

	<Import Project="..\common.props"></Import>
	



	<ItemGroup>
	  <None Remove="Data\Scripts\Course.backup.sql" />
	  <None Remove="Data\Scripts\course.view.sql" />
	</ItemGroup>

	

  <ItemGroup>
    <ProjectReference Include="..\..\tec\Tec.Rad.Extensions\Tec.Rad.Extensions.csproj" />
    <ProjectReference Include="..\..\tec\Tec.Rad.Extensions.Data\Tec.Rad.Extensions.Data.csproj" />
    <ProjectReference Include="..\Ecademy.Platform\Ecademy.Platform.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

</Project>
