using Microsoft.OpenApi.Models;
using Tec.Ecademy.Academics;
using Tec.Ecademy.Content;
using Tec.Ecademy.Platform;
using Tec.Rad.Apis;
using Tec.Rad.Infra.Core;
using Tec.Rad.Infra.Core.Conventions;
using Tec.Rad.Modular;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;

namespace Tec.Ecademy.Sys.ApiDev;

/// <summary>
/// Lightweight module for NSwag generation that only registers API endpoints
/// without database, authentication, or other heavy services
/// </summary>
[DependsOn(
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpSwashbuckleModule),
    typeof(RadAspNetModule),

    // Only the core modules for API discovery - no infrastructure
    typeof(ContentModule),
    typeof(AcademicsModule),
    typeof(PlatformModule)
)]
public class EcademyNSwagModule : RadModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        // Configure API options for all modules
        PreConfigure<RadApiOptions>(options =>
        {
            // Content module
            options.Register<ContentModule>("content", "Content", "content")
                .AddSliceControllers("Features");

            // Academics module
            options.Register<AcademicsModule>("academics", "Academics", "academics")
                .AddSliceControllers("Features");

            // Platform module
            options.Register<PlatformModule>("platform", "Platform", "platform")
                .AddSliceControllers("Features");
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var services = context.Services;

        // Configure minimal MVC
        services.AddControllers();

        // Configure Swagger for all modules
        ConfigureSwagger(services);

        // Skip heavy services - no database, no auth, no caching, etc.
        // Only what's needed for API discovery and Swagger generation
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();

        // Minimal middleware pipeline
        app.UseRouting();
        app.UseSwagger();
        app.UseSwaggerUI(options =>
        {
            var apiOptions = context.ServiceProvider.GetRequiredService<RadApiOptions>();

            // Add swagger endpoints for each module
            foreach (var apiGroup in apiOptions.GetApiGroups().OrderBy(x => x.ApiName))
            {
                var endpoint = $"/swagger/{apiGroup.ModuleName}/swagger.json";
                options.SwaggerEndpoint(endpoint, $"{apiGroup.ApiName} API");
            }
        });

        app.UseConfiguredEndpoints();
    }

    private void ConfigureSwagger(IServiceCollection services)
    {
        services.AddSwaggerGen(options =>
        {
            // Get API options to configure swagger docs
            var serviceProvider = services.BuildServiceProvider();
            var apiOptions = serviceProvider.GetService<RadApiOptions>();

            if (apiOptions != null)
            {
                foreach (var apiGroup in apiOptions.GetApiGroups())
                {
                    options.SwaggerDoc(apiGroup.ModuleName, new OpenApiInfo
                    {
                        Title = apiGroup.ApiName,
                        Version = apiGroup.Version
                    });
                }
            }

            // Configure doc inclusion predicate
            options.DocInclusionPredicate((docName, apiDesc) =>
            {
                if (apiOptions == null) return false;
                return ApiSpecHelper.ApiDocSelector(apiOptions, docName, apiDesc);
            });

            // Minimal schema configuration
            options.CustomSchemaIds(type => type.FullName?.Replace("+", "."));
        });
    }
}