﻿using Ecademy.Common.Common;
using Ecademy.Common.Core;
using Tec.Rad.Annotations;
using Tec.Rad.Utils.Timing;

namespace Tec.Ecademy.Academics.Domain;

/// <summary>
/// Per-student instance of a Coursework (allocation + learner state).
/// Created when Coursework is published/assigned to the student.
/// </summary>
public class Assignment : DocEntity
{
    

 
    private List<AssignmentAttempt> _attemptsList = new List<AssignmentAttempt>();
    private List<StatusHistory> _statusHistoryList = new List<StatusHistory>();

    public Assignment(Guid id) : base(id)
    {

    }

    public Assignment(Guid id, Coursework coursework, Guid userId) : base(id)
    {
        CourseworkId = coursework.Id;
        UserId = userId;
            
    }

    public Guid CourseRunId { get; set; }
    
    public Guid UserId { get; init; }

    public Guid CourseworkId { get; init; }

    
    public DateTimeOffset? SubmittedTime { get; private set; }

    public AssignmentStatus Status { get; private set; } = AssignmentStatus.Due;

    public Guid? CurrentAttemptId { get; private set; }

    [JsonData]
    public IReadOnlyList<AssignmentAttempt> Attempts
    {
        get => (_attemptsList ??= new List<AssignmentAttempt>()).AsReadOnly();
        init => _attemptsList = value.ToList();
    }

    [JsonData]
    public GradeResult Result { get; private set; }

    [JsonData]
    public IReadOnlyList<StatusHistory> StatusHistory
    {
        get => (_statusHistoryList ??= new List<StatusHistory>()).AsReadOnly();
        init => _statusHistoryList = value.ToList();
    }

    public AssignmentAttempt StartAttempt()
    {
        var attempt = new AssignmentAttempt(this);
        attempt.StartTime = SystemTime.Now;
        CurrentAttemptId = attempt.Id;
        this.Status = AssignmentStatus.InProgress;
        this._attemptsList.Add(attempt);
        return attempt;
    }

    public void Submit()
    {
        this.ChangeStatus(AssignmentStatus.Submitted, SystemTime.Now);
    }

    public void Grade(Guid attemptId, GradeResult grade, DateTime timeStamp)
    {
            
        var attempt = this.Attempts.FirstOrDefault(n => n.Id == attemptId);
        if (attempt != null)
        {
            attempt.AssignGrade(grade, timeStamp);
        }
        Result = grade;
        ChangeStatus(AssignmentStatus.Graded, timeStamp);
    }

    public void ChangeStatus(AssignmentStatus submissionStatus, DateTime timeStamp)
    {
        Status = submissionStatus;
        this._statusHistoryList.Add(new StatusHistory(submissionStatus.ToString(), timeStamp));

    }


        
}