﻿using Tec.Ecademy.Academics.ReadModel;
using Tec.Rad.Apis;
using Tec.Rad.SqlKata;

namespace Tec.Ecademy.Academics.Features.Schedule;

public class GetSegmentDetail
{
    public class Endpoint: GetEndpoint<Input, SubsectionDetailOutput>
    {
            
    }

    public class Input: IQueryRequest<SubsectionDetailOutput>
    {
        public Guid SubsectionId { get; set; }
    }


    public class SubsectionDetailOutput: ResultObject
    {
            
        public Segment Segment { get; set; }
        public List<AssignmentInfo> Activities { get; set; }

    }


    internal class Executor: DocDbQueryExecutor<Input, SubsectionDetailOutput>
    {
            
            
        protected override async Task Execute(CancellationToken cancellationToken)
        {
            Output.Segment = await DocDb.GetAsync<Segment>(Input.SubsectionId);
               
            Output.Activities = await Db.ExpressionQuery<AssignmentInfo>()
                .ToListAsync(n=>n.SubsectionId, Input.SubsectionId);

        }


            

           
    }

}