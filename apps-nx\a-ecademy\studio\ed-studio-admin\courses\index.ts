import { ModuleInfo } from "@tec/rad-core/abstractions";
import { radCompositeRoute } from '@tec/rad-core/composition';


export const AdminCourseModule: ModuleInfo = {
    name: 'AdminCourseModule',
    path: "courses",
    routes: [
        {
            name: 'CourseList', path: 'list',
            loadComponent: () => import('./feature-list').then(m => m.CourseList),
        },
        {
            name: 'Course', path: ':courseId', id:'courseId',
            loadComponent: () => import('./feature-course').then(m => m.CoursePage),
            children: [
                ...radCompositeRoute("CourseDetail"),
            ]
        }
        
    ],
    views: [
        {name: 'CourseDetail', loadComponent: () => import('./feature-course').then((m) => m.CourseDetailView)},
        {name: 'CourseRuns', loadComponent: () => import('./feature-course-runs').then((m) => m.CourseRunsView)},
    ]

}